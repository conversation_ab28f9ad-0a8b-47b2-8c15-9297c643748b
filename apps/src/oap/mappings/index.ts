import { ApplicationMappingConfig, UEG_APPLICATION_MAPPINGS } from './ueg-mappings';

// Registry of all brand mappings
export const BRAND_MAPPINGS: Record<string, ApplicationMappingConfig> = {
  'UEG': UEG_APPLICATION_MAPPINGS,
  // Add other brand mappings here as they are created
  // 'UCW': UCW_APPLICATION_MAPPINGS,
};

/**
 * Get application mapping configuration for a specific brand
 * @param brand - Brand identifier (e.g., 'UEG', 'IBAT')
 * @returns ApplicationMappingConfig for the brand
 * @throws Error if brand mapping is not found
 */
export function getBrandMappings(brand: string): ApplicationMappingConfig {
  const mappings = BRAND_MAPPINGS[brand.toUpperCase()];
  
  if (!mappings) {
    throw new Error(`No application mappings found for brand: ${brand}. Available brands: ${Object.keys(BRAND_MAPPINGS).join(', ')}`);
  }
  
  return mappings;
}

/**
 * Check if a brand has mapping configuration
 * @param brand - Brand identifier
 * @returns boolean indicating if mappings exist
 */
export function hasBrandMappings(brand: string): boolean {
  return BRAND_MAPPINGS.hasOwnProperty(brand.toUpperCase());
}

/**
 * Get list of all supported brands
 * @returns Array of brand identifiers
 */
export function getSupportedBrands(): string[] {
  return Object.keys(BRAND_MAPPINGS);
}

export { ApplicationMappingConfig } from './ueg-mappings';
