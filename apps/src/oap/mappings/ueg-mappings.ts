export interface ApplicationMappingConfig {
  directFields: Record<string, string | string[]>;
  documentFields: Record<string, string | string[]>;
  arrayFields: Record<string, string | string[]>;
  subsectionMappings: Record<string, Record<string, string | string[]>>;
  picklistMappings: Record<string, Record<string, string>>;
  transformationRules: Record<string, any>;
}

export const UEG_APPLICATION_MAPPINGS: ApplicationMappingConfig = {
  directFields: {
    // Applicant Details
    "data.firstName": "firstName",
    "data.legalFamilyName": "lastName",
    "data.personalEmail": "email",
    "data.dateOfBirth": "birthDate",
    "data.placeOfBirth": "placeOfBirth",
    "data.countryOfBirth": "countryOfBirth",
    "data.citizenship": "citizenshipDisplayName",
    "data.correspondenceLanguage": ["correspondenceLanguage", "correspondenceLanguageDisplayName"],
    "data.applyReferFriendScheme": "isReferFriend",
    "data.referIdNumber": "ueIDNumber",
    "data.referFirstName": "referralFirstName",
    "data.referSurname": "referralLastName",
    "data.title": ["title", "titleDisplayName"],
    "data.phoneHome": "phoneNumber",
    "data.phone": "otherNumber",

    // Declaration & Data Protection
    "data.declarationInfoTrue": "declaration1",
    "data.personalInfoTrue": "declaration2",

    // Higher Education
    "data.higherEducationExits": "isPreviousHigherEducationEnrollment",
    "data.educationMainCountry": "countryOfInitialRegistration",
    "data.educationMainGermanUniversity": ["universityOfInitialRegistration", "universityOfInitialRegistrationDisplayName"],
    "data.educationMainName": "otherUniversityName",
    "data.educationMainSemesterYearMonth": ["semesterOfInitialRegistration", "semesterOfInitialRegistrationDisplayName"],
    "data.educationAnotherUniversityEnrolledGermany": "isEnrolledInAnotherUniversity",

    // Correspondence Address
    "data.correspondenceStreetAddress": "correspondenceStreetAddress",
    "data.correspondenceCity": "correspondenceCity",
    "data.correspondenceState": "correspondenceState",
    "data.correspondenceCountry": "correspondenceCountryDisplayName",
    "data.correspondencePostalCode": "correspondencePostalCode",

    // Permanent Address
    "data.mailingAddress": "streetAddress",
    "data.city": "city",
    "data.country": "countryDisplayName",
    "data.state": [
      "mailingState",
      "mailingStateDisplayName"
    ],
    "data.postalCode": "postalCode",
    "data.addressCo": "careOf",
    "data.correspondenceAddress": "isCorrespondanceAddressDiffer",

    // Program Information
    "data.program": ["program", "programDisplayName"],
    "data.level": ["programTypeDisplayName", "programType"],
    "data.language": [
      "language",
      "languageDisplayName"
    ],
    "data.location": [
      "location",
      "locationDisplayName"
    ],
    "data.duration": [
      "duration",
      "programDurationDisplayName"
    ],
    "data.intake": "productId",

    // Application Metadata
    "uuid": "applicationId",
    "userId": "legacyUserId",
    "sfProgramId": "sfProgramId",
    "submittedAt": "submittedAt",
    "createdAt": "createdAt",
    "updatedAt": "updatedAt",
    "isSubmitted": "isSubmitted"
  },

  documentFields: {
    "data.fileCV": "CV",
    "data.photo": "photograph",
    "data.educationSchoolsCertificateFile": "HEEQ",
    "data.fileGermanTest": "languageCertificateGermen",
    "data.fileEnglishTest": "languageCertificateEnglish",
    "data.fileEntranceTest": "entranceTest",
    "data.filePortfolio": "portfolio",
    "data.applyReasonFile": "motivationInformation",
    "data.nonObjectionCertificate": "noObjectionCertificate",
    "data.schoolLeavingCertificate": "deregistrationCertificate",
    "data.otherFiles": "otherCertificate",
    "data.fileEducationQualification": "finalHEEQ",
    "data.educationSchoolsTranscriptFile": "bachelorTranscript"
  },

  arrayFields: {
    "data.educationSchools": "educationInfo",
    "data.eqhe": "eqheDetails"
  },

  subsectionMappings: {
    educationInfo: {
      "educationSchoolsType": ["educationDetailsEntryType", "educationDetailsEntryTypeDisplayName"],
      "educationSchoolsStudyStatus": ["educationStatus", "educationStatusDisplayName"],
      "educationSchoolsExamDate": "examDate",
      "educationSchoolsFirstEnrolmentYear": "firstEnrolmentDate",
      "educationSchoolsLastEnrolmentYear": "lastEnrolmentDate",
      "educationSchoolsSpecialisation": "programmeCompleted",
      "educationSchoolsDegree": ["degree", "degreeDisplayName"],
      "educationSchoolsSpecialisationName": ["specialisation", "specialisationDisplayName"],
      "educationSchoolsPartnerUniversity": ["partnerUniversityName", "partnerUniversityNameDisplayName"],
      "educationSchoolsPartnerUniversityOther": "otherPartnerUniversityName",
      "educationSchoolsUniversity": ["universityName", "universityNameDisplayName"],
      "educationSchoolsPresenceOrDistanceType": ["attendanceType", "attendanceTypeDisplayName"],
      "educationSchoolsStudyTimeType": ["studyMode", "studyModeDisplayName"],
      "educationSchoolsGrade": ["finalGrade", "finalGradeDisplayName"],
      "educationSchoolsCountry": "awardingCountryDisplayName",
      "educationSchoolsCity": "educationCity",
      "educationSchoolsDetails": "educationRemark"
    },
    eqheDetails: {
      "eqheDate": "dateOfEQHE",
      "eqheCity": "cityOfEQHE",
      "eqhecountry": "countryOfEQHEDisplayName",
      "eqheTitle": "originalTitleOfEQHE"
    }
  },

  picklistMappings: {
    programType: {
      "Undergraduate": "a0S0O00000UAl6iUAD",
      "Preparatory Programme": "a0S0O00000W5A7dUAF",
      "Postgraduate": "a0S0O00000UAl6nUAD",
      "Undergraduate + Foundation": "a0S6700001N7yM8EAJ"
    },
    language: {
      "English": "English",
      "German": "German",
      "Spanish": "Spanish",
      "French": "French"
    },
    title: {
      "mr": "Mr",
      "mrs": "Mrs",
      "ms": "Ms",
      "miss": "Miss",
    },
    titleDisplayName: {
      "mr": "Mr",
      "mrs": "Mrs",
      "ms": "Ms",
      "miss": "Miss",
    },
    correspondenceLanguage: {
      "german": "Deutsch - German",
      "english": "Englisch - English"
    },
    correspondenceLanguageDisplayName: {
      "german": "Deutsch - German",
      "english": "Englisch - English"
    },
    programDisplayName: {
      "a010X000012tJ3vQAE": "BA Communication Design",
      "a010X000012tM1DQAU": "BA Film and Motion Design",
      "a010X000012tM1EQAU": "BA Illustration",
      "a010X000012tM1GQAU": "BA Sports Science (Fitness and Health)",
      "a010X000012tM1HQAU": "BA Sports Science (Training and Performance)",
      "a010X000012tM1IQAU": "BSc Business and Management Studies",
      "a010X000012tM1JQAU": "BSc Business Psychology",
      "a010X000012tM1KQAU": "BSc Digital Media and Marketing",
      "a010X000012tM1LQAU": "BSc Digital Business and Data Science",
      "a010X000012tM1MQAU": "BSc Sport and Event Management",
      "a010X000012tM1TQAU": "Studienkolleg (Foundation Year - Business)",
      "a010X000012tM1UQAU": "Studienkolleg (Foundation Year - Technology)",
      "a010X000012tM1WQAU": "MA International Sport and Event Management",
      "a010X000012tM1XQAU": "MA Marketing Management",
      "a010X000012tM1YQAU": "MA New Media Design",
      "a010X000012tM1ZQAU": "MA Visual and Experience Design",
      "a010X000012tM1bQAE": "MSc Corporate Management",
      "a010X000012tM1dQAE": "Pre-master Business",
      "a010X000012tM1xQAE": "BA Game Design",
      "a010X000012tMj6QAE": "BA Photography and New Media",
      "a010X000012tMksQAE": "BSc Psychology",
      "a010X000012tMmZQAU": "MA Photography",
      "a010X000012tMmaQAE": "MBA Master of Business Administration",
      "a010X000013oMuSQAU": "MA Innovation Design Management",
      "a0167000017zeJRAAY": "BA Digital Product Management",
      "a0167000017zeJSAAY": "BSc Software Engineering",
      "a0167000017zeJTAAY": "MSc Data Science",
      "a0167000017zeJlAAI": "BA UI/UX Design",
      "a0167000017zeJmAAI": "MSc Software Engineering",
      "a0167000017zyCxAAI": "BA Communication Design (HTK Top-up)",
      "a01670000180m2uAAA": "MSc Psychology Rehabilitation",
      "a01670000180m2vAAA": "MSc Psychology Coaching",
      "a016700001CjFC3AAN": "BSc Sport and Event Management (Dual)",
      "a016700001CjFC8AAN": "BSc Digital Media and Marketing (Dual)",
      "a016700001CjFCSAA3": "BSc Software Engineering (Dual)",
      "a016700001CjFLPAA3": "BSc Business and Taxes (Dual)",
      "a016700001CjUtQAAV": "MA Communication Design",
      "a016700001CjnBnAAJ": "MBA Diplomacy",
      "a016700001CjnBsAAJ": "MBA Digital Technology",
      "a016700001CjnBxAAJ": "MBA Sustainable Water Management",
      "a016700001CjnC2AAJ": "MBA Shipping and Logistics",
      "a016700001CjnCbAAJ": "MSc International Business Management",
      "a016700001CjwgzAAB": "BA Hotel Management (dual)",
      "a016700001CkOryAAF": "B.Sc.Digital Business and Data Science with Foundation Diploma",
      "a016700001CkOs3AAF": "B.Sc. Digital Media and Marketing with Foundation Diploma",
      "a016700001CkOs9AAF": "B.Sc. Sport and Event Management with Foundation Diploma",
      "a016700001CkOsAAAV": "B.A. Film + Motion Design with Foundation Diploma",
      "a016700001CkOsDAAV": "B.Sc. Software Engineering with Foundation Diploma",
      "a016700001CkOsIAAV": "B.Sc. Business and Management Studies with Foundation Diploma",
      "a016700001CkOsJAAV": "B.A. Game Design with Foundation Diploma",
      "a016700001CkOsKAAV": "B.A. Illustration with Foundation Diploma",
      "a016700001CkOsNAAV": "B.A. Communication Design with Foundation Diploma",
      "a016700001CkOsSAAV": "B.A. Photography with Foundation Diploma",
      "a016700001CkOsXAAV": "B.A. UX/UI Design with Foundation Diploma",
      "a016700001HucXNAAZ": "MSc International Public Health Management",
      "a016700001HucXSAAZ": "MSc Asset Management",
      "a016700001HucXcAAJ": "MSc International Logistics and Transportation Management",
      "a016700001JXG8eAAH": "B.Sc. Psychology with Foundation Diploma",
      "a016700001KTxmbAAD": "MBA Financial Management",
      "a016700001KTxmlAAD": "MBA Investment Banking",
      "a016700001KTxmmAAD": "MBA Management Consulting",
      "a016700001KTxmnAAD": "MBA Health Service Management",
      "a016700001KTxmqAAD": "MBA Marketing Management",
      "a016700001KTxmrAAD": "MBA Sales Management",
      "a016700001KTxmvAAD": "MBA Project Management",
      "a016700001KTxn0AAD": "MBA Financial Risk Management",
      "a016700001KTxnAAAT": "MBA International Business",
      "a016700001KTxnFAAT": "MBA Health and Safety Leadership",
      "a016700001KTxnGAAT": "MBA Sports Management",
      "a016700001KTxnKAAT": "MBA Digital Health Transformation",
      "a016700001KTxnPAAT": "MSc Prevention and Therapy Management",
      "a016700001KTxnUAAT": "MA Design Leadership",
      "a016700001KTxnZAAT": "MA Digital Content Creation",
      "a016700001KTxneAAD": "BA Visual Communication",
      "a016700001KTxnoAAD": "BA Design and Management Studies",
      "a016700001KTxnuAAD": "B.Sc. Business Psychology with Foundation Diploma",
      "a016700001KU3gyAAD": "B.A. Digital Product Management with Foundation Diploma",
      "a01Tf000003jsk8IAA": "MSc Digital Transformations",
      "a01Tf000003k9EiIAI": "BSc Health Management",
      "a01Tf0000067WsYIAU": "MA Generative Design and AI",
      "a01Tf000006s66hIAA": "MSc Project Management International Programme",
      "a01Tf000006snrWIAQ": "MSc Strategic Business Management International Programme",
      "a01Tf000006snzaIAA": "MSc Marketing International Programme",
      "a01Tf000008ur1vIAA": "MSc Psychology Coaching and Counseling",
      "a01Tf0000098mAOIAY": "BA Sports Science (Fitness and Health) with Foundation Diploma",
      "a01Tf0000098mX1IAI": "BA Visual Communication with Foundation Diploma",
      "a01Tf0000098qilIAA": "BSc Software Engineering (Dual) with Foundation Diploma",
      "a01Tf0000098qxAIAQ": "BA Hotel Management (Dual) with Foundation Diploma",
      "a01Tf0000098u4iIAA": "BA Design and Management Studies with Foundation Diploma",
      "a01Tf00000993MUIAY": "BA Sports Science (Training and Performance) with Foundation Diploma",
      "a01Tf000009BW4HIAW": "BSc Health Management with Foundation Diploma",
      "a01Tf00000EaVzoIAF": "BSc Sport and Event Management with Pre-Course",
      "a01Tf00000EalEtIAJ": "BSc Software Engineering with Pre-Course",
      "a01Tf00000EamajIAB": "Pre-Master Psychology",
      "a01Tf00000EaoMRIAZ": "BA Design and Management Studies with Pre-Course"
    },
    degreeType: {
      "Other": "Other",
      "Bachelor of Arts (B.A.)": "BS",
      "Bachelor of Education (B.Ed.)": "BS",
      "Bachelor of Engineering (B.Eng.)": "BS",
      "Bachelor of Fine Arts (B.F.A.)": "BS",
      "Bachelor of Laws (LL.B.)": "JD",
      "Bachelor of Music (B.Mus.)": "BS",
      "Bachelor of Musical Arts (B.M.A.)": "BS",
      "Bachelor of Science (B.Sc.)": "BS",
      "Diplom": "Other",
      "Diplom (FH)": "Other",
      "Magister": "Other",
      "Master of Arts (M.A.)": "MA",
      "Master of Education (M.Ed.)": "MS",
      "Master of Engineering (B.Eng.)": "MS",
      "Master of Fine Arts (M.F.A.)": "MA",
      "Master of Laws (LL.M.)": "JD",
      "Master of Music (M.Mus.)": "MA",
      "Master of Musical Arts (M.M.A.)": "MA",
      "Master of Science (M.Sc.)": "MS"
    },
    startTerm: {
      "01t6700000E4lmR": "2025-09-01",
      "01tTf000004RLak": "2026-03-01",
      "01tTf000004RO7B": "2026-09-01",
      "01t6700000E4ln0": "2025-09-01",
      "01tTf000004ROGr": "2026-09-01",
      "01tTf000004ROIT": "2026-03-01",
      "01t6700000E4lnA": "2025-09-01",
      "01tTf000004RNPe": "2026-03-01",
      "01tTf000004RONJ": "2026-09-01",
      "01tTf000002S3eX": "2025-09-01",
      "01tTf000004ROQX": "2026-03-01",
      "01tTf000004ROS9": "2026-09-01",
      "01tTf000002S3g9": "2025-09-01",
      "01tTf000004ROTl": "2026-03-01",
      "01tTf000004ROVN": "2026-09-01",
      "01t6700000E4loD": "2025-09-01",
      "01tTf000004RKml": "2026-03-01",
      "01tTf000004RMTa": "2026-09-01",
      "01tTf000004RNB8": "2026-09-01",
      "01tTf000004ROjt": "2026-09-01",
      "01tTf000004ROlV": "2026-03-01",
      "01tTf000004ROn7": "2026-03-01",
      "01tTf000004ROoj": "2026-03-01",
      "01tTf000004ROqL": "2026-09-01",
      "01tTf000004VICr": "2025-09-01",
      "01tTf000004VICs": "2025-09-01",
      "01tTf000004VIET": "2025-09-01",
      "01t6700000E4llo": "2025-09-01",
      "01t6700000E4loS": "2025-09-01",
      "01tTf000004ROwn": "2026-09-01",
      "01tTf000004ROyP": "2026-03-01",
      "01tTf000004RP1d": "2026-09-01",
      "01tTf000004RP4r": "2026-03-01",
      "01t6700000E4lor": "2025-09-01",
      "01t6700000E4lzk": "2025-09-01",
      "01tTf000004RLUI": "2026-03-01",
      "01tTf000004RPG9": "2026-09-01",
      "01tTf000004RPHl": "2026-09-01",
      "01tTf000004RPJN": "2026-03-01",
      "01t6700000E4loh": "2025-09-01",
      "01t6700000E4lom": "2025-09-01",
      "01t6700000E4lzf": "2025-09-01",
      "01tTf000004RNeA": "2026-09-01",
      "01tTf000004RP6T": "2026-03-01",
      "01tTf000004RP85": "2026-09-01",
      "01tTf000004RP9h": "2026-03-01",
      "01tTf000004RPBJ": "2026-09-01",
      "01tTf000004RPCv": "2026-03-01",
      "01t6700000E4lpf": "2025-09-01",
      "01t6700000E4lpk": "2025-09-01",
      "01t6700000E4lpp": "2025-09-01",
      "01tTf000004RPjB": "2026-09-01",
      "01tTf000004RPkn": "2026-03-01",
      "01tTf000004RPo1": "2026-09-01",
      "01tTf000004RPpd": "2026-03-01",
      "01tTf000004RPrF": "2026-09-01",
      "01tTf000004RPsr": "2026-03-01",
      "01tTf000006MF3O": "2025-09-01",
      "01tTf000006MF3P": "2026-03-01",
      "01tTf000006MFmY": "2026-09-01",
      "01t6700000E4lwv": "2025-09-01",
      "01t6700000E4lx0": "2025-09-01",
      "01t6700000E4lx5": "2025-09-01",
      "01tTf000004VJiP": "2026-09-01",
      "01tTf000004VJk1": "2026-09-01",
      "01tTf000004VJld": "2026-09-01",
      "01tTf000004VJth": "2026-03-01",
      "01tTf000004VJvJ": "2026-03-01",
      "01tTf000004VJwv": "2026-03-01",
      "01t6700000E4lxF": "2025-09-01",
      "01t6700000E4lxK": "2025-09-01",
      "01tTf000004VJor": "2026-09-01",
      "01tTf000004VJqT": "2026-09-01",
      "01t6700000E4lqT": "2025-09-01",
      "01t6700000E4lqY": "2025-09-01",
      "01t6700000E4lqd": "2025-09-01",
      "01tTf000004RPMc": "2026-09-01",
      "01tTf000004RPmQ": "2026-09-01",
      "01tTf000004RQYn": "2026-09-01",
      "01tTf000004RQaP": "2026-03-01",
      "01tTf000004RQc1": "2026-03-01",
      "01tTf000004RQdd": "2026-03-01",
      "01t6700000E4lqi": "2025-09-01",
      "01t6700000E4lqn": "2025-09-01",
      "01t6700000E4lqs": "2025-09-01",
      "01tTf000004RM5P": "2026-09-01",
      "01tTf000004RO2M": "2026-09-01",
      "01tTf000004ROdS": "2026-03-01",
      "01tTf000004RQfF": "2026-09-01",
      "01tTf000004RQgr": "2026-03-01",
      "01tTf000004RQiT": "2026-03-01",
      "01t6700000E4lrH": "2025-09-01",
      "01t6700000E4lrM": "2025-09-01",
      "01t6700000E4lrR": "2025-09-01",
      "01tTf000004RM70": "2026-03-01",
      "01tTf000004RQk5": "2026-09-01",
      "01tTf000004RQlh": "2026-09-01",
      "01tTf000004RQnJ": "2026-09-01",
      "01tTf000004RQov": "2026-03-01",
      "01tTf000004RQs9": "2026-03-01",
      "01t6700000E4lrI": "2025-09-01",
      "01t6700000E4lrv": "2025-09-01",
      "01t6700000E4m0J": "2025-09-01",
      "01t6700000E4m0O": "2025-09-01",
      "01t6700000E4m0T": "2025-09-01",
      "01tTf000004RKTO": "2026-03-01",
      "01tTf000004RLcO": "2026-09-01",
      "01tTf000004RMtP": "2026-09-01",
      "01tTf000004RNZK": "2026-03-01",
      "01tTf000004RNfn": "2026-03-01",
      "01tTf000004RQyb": "2026-09-01",
      "01tTf000004RR0D": "2026-09-01",
      "01tTf000004RR1p": "2026-09-01",
      "01tTf000004RR3R": "2026-03-01",
      "01tTf000004RR53": "2026-03-01",
      "01t6700000E4ltr": "2025-09-01",
      "01t6700000E4ltw": "2025-09-01",
      "01t6700000E4lu1": "2025-09-01",
      "01tTf000004RNuI": "2026-09-01",
      "01tTf000004RQdf": "2026-09-01",
      "01tTf000004RS4L": "2026-09-01",
      "01tTf000004RS7Z": "2026-03-01",
      "01tTf000004RS9B": "2026-03-01",
      "01tTf000004RSAn": "2026-03-01",
      "01t6700000E4lwg": "2025-09-01",
      "01t6700000E4lwl": "2025-09-01",
      "01t6700000E4lwq": "2025-09-01",
      "01tTf000003fORH": "2025-09-01",
      "01tTf000003fORI": "2026-03-01",
      "01tTf000004RKI6": "2026-09-01",
      "01tTf000004ROX0": "2026-03-01",
      "01tTf000004RP6U": "2026-03-01",
      "01tTf000004RTIA": "2026-09-01",
      "01tTf000004RTLN": "2026-09-01",
      "01tTf000004RTMz": "2026-03-01",
      "01tTf000004RTOb": "2026-09-01",
      "01t6700000E4ln5": "2025-09-01",
      "01tTf000004RNHa": "2026-09-01",
      "01tTf000004ROK5": "2026-03-01",
      "01t6700000E4lnF": "2025-09-01",
      "01tTf000004RM5O": "2026-09-01",
      "01tTf000004ROOv": "2026-03-01",
      "01t6700000E4lpL": "2025-09-01",
      "01t6700000E4lpQ": "2025-09-01",
      "01tTf000004RJKW": "2026-09-01",
      "01tTf000004RK3a": "2026-03-01",
      "01tTf000004RPUf": "2026-09-01",
      "01tTf000004RPWH": "2026-03-01",
      "01t6700000E4lrW": "2025-09-01",
      "01t6700000E4lrb": "2025-09-01",
      "01t6700000E4lrg": "2025-09-01",
      "01tTf000004RI0A": "2026-03-01",
      "01tTf000004RPRS": "2026-03-01",
      "01tTf000004RQYo": "2026-09-01",
      "01tTf000004RQtl": "2026-09-01",
      "01tTf000004RQvN": "2026-09-01",
      "01tTf000004RQwz": "2026-03-01",
      "01t6700000E4lt3": "2025-09-01",
      "01t6700000E4lt8": "2025-09-01",
      "01t6700000E4ltD": "2025-09-01",
      "01tTf000004RLu6": "2026-07-01",
      "01tTf000004RReX": "2027-01-01",
      "01tUC000008Cj1a": "2026-01-01",
      "01t6700000E4lqJ": "2025-09-01",
      "01t6700000E4lqO": "2025-09-01",
      "01t6700000E4lzM": "2025-09-01",
      "01t6700000E4m09": "2025-09-01",
      "01t6700000E4m0E": "2025-09-01",
      "01tTf000004RJyl": "2026-03-01",
      "01tTf000004RKy5": "2026-03-01",
      "01tTf000004RLpG": "2026-03-01",
      "01tTf000004RMII": "2026-09-01",
      "01tTf000004RMoc": "2026-09-01",
      "01tTf000004RNeB": "2026-09-01",
      "01tTf000004RQSL": "2026-09-01",
      "01tTf000004RQTx": "2026-03-01",
      "01tTf000004RQVZ": "2026-09-01",
      "01tTf000004RQXB": "2026-03-01",
      "01t6700000E4lzV": "2025-09-01",
      "01tTf000004ROC1": "2026-09-01",
      "01tTf000004ROFF": "2026-03-01",
      "01t6700000E4lzp": "2025-09-01",
      "01tTf000004RPb7": "2026-09-01",
      "01tTf000004RPcj": "2026-03-01",
      "01tTf000006MFuh": "2025-09-01",
      "01tTf000006MFui": "2026-03-01",
      "01tTf000006MJLV": "2026-09-01",
      "01t6700000E4luG": "2025-09-01",
      "01t6700000E4luL": "2025-09-01",
      "01t6700000E4m0d": "2025-09-01",
      "01t6700000E4m0i": "2025-09-01",
      "01t6700000E4m0n": "2025-09-01",
      "01tTf000004RK8Q": "2026-09-01",
      "01tTf000004RKN3": "2026-09-01",
      "01tTf000004RMgV": "2026-09-01",
      "01tTf000004RPJO": "2026-03-01",
      "01tTf000004RRWU": "2026-03-01",
      "01tTf000004RRxu": "2026-03-01",
      "01tTf000004RSCP": "2026-09-01",
      "01tTf000004RSE1": "2026-03-01",
      "01tTf000004RSFd": "2026-03-01",
      "01tTf000004RSHF": "2026-09-01",
      "01t6700000E4lza": "2025-09-01",
      "01tTf000004ROGs": "2026-09-01",
      "01tTf000004ROWz": "2026-03-01",
      "01t6700000E4lqF": "2025-09-01",
      "01t6700000E4lwM": "2025-09-01",
      "01t6700000E4lwR": "2025-09-01",
      "01t6700000E4m0s": "2025-09-01",
      "01t6700000E4m0x": "2025-09-01",
      "01tTf000004RLFm": "2026-09-01",
      "01tTf000004RLal": "2026-03-01",
      "01tTf000004RNhO": "2026-09-01",
      "01tTf000004RPo3": "2026-09-01",
      "01tTf000004RQqZ": "2026-03-01",
      "01tTf000004RTA5": "2026-09-01",
      "01tTf000004RTBh": "2026-09-01",
      "01tTf000004RTDJ": "2026-03-01",
      "01tTf000004RTEv": "2026-03-01",
      "01tTf000004RTGX": "2026-03-01",
      "01t6700000B8lZ8": "2026-03-01",
      "01t6700000E5uVB": "2026-03-01",
      "01t6700000E5uVG": "2026-03-01",
      "01t6700000E5uVL": "2026-03-01",
      "01t6700000E5uVQ": "2026-03-01",
      "01t6700000E4lqZ": "2025-09-01",
      "01t6700000E4lw2": "2025-09-01",
      "01tTf000004RRjO": "2026-09-01",
      "01tTf000004RT5F": "2026-03-01",
      "01tTf000004RT6r": "2026-09-01",
      "01tTf000004RT8T": "2026-03-01",
      "01t6700000E4lvs": "2025-09-01",
      "01t6700000E4lvx": "2025-09-01",
      "01tTf000004RNJC": "2026-03-01",
      "01tTf000004RPXu": "2026-09-01",
      "01tTf000004RT21": "2026-09-01",
      "01tTf000004RT3d": "2026-03-01",
      "01t6700000E4lpu": "2025-09-01",
      "01t6700000E4lpz": "2025-09-01",
      "01tTf000004RJ7d": "2026-09-01",
      "01tTf000004RPuT": "2026-09-01",
      "01tTf000004RPw5": "2026-03-01",
      "01tTf000004h4UI": "2026-03-01",
      "01t6700000E4loO": "2025-09-01",
      "01t6700000E4lpB": "2025-09-01",
      "01tTf000004RNFy": "2026-03-01",
      "01tTf000004RPKz": "2026-09-01",
      "01tTf000004RPMb": "2026-03-01",
      "01tTf000004RPOD": "2026-09-01",
      "01t6700000DCCwr": "2025-09-01",
      "01tTf000004RPfx": "2026-09-01",
      "01tTf000005770I": "2026-03-01",
      "01t6700000E4loI": "2025-09-01",
      "01t6700000E4loN": "2025-09-01",
      "01tTf000004ROtZ": "2026-09-01",
      "01tTf000004ROvB": "2026-09-01",
      "01t6700000E4lmh": "2025-09-01",
      "01t6700000E4lqE": "2025-09-01",
      "01tTf000004RKtC": "2026-09-01",
      "01tTf000004RL1G": "2026-03-01",
      "01tTf000004RLkQ": "2026-03-01",
      "01tTf000004RQ0w": "2026-09-01",
      "01tTf000004RQ2X": "2026-03-01",
      "01tTf000004RQ49": "2026-09-01",
      "01tTf000004VIEU": "2025-09-01",
      "01t6700000E4lsF": "2025-09-01",
      "01tTf000002H7iH": "2025-09-01",
      "01tTf000004RN7u": "2026-09-01",
      "01tTf000004RNmF": "2026-03-01",
      "01tTf000004RPCw": "2026-03-01",
      "01tTf000004RQde": "2026-09-01",
      "01t6700000E4m0Y": "2025-09-01",
      "01tTf000004RKYE": "2026-09-01",
      "01tTf000004RNmE": "2026-03-01",
      "01t6700000E4ltc": "2025-09-01",
      "01tTf000004RRLC": "2026-03-01",
      "01tTf000004RRxt": "2026-09-01",
      "01t6700000E4lox": "2025-09-01",
      "01tTf000002H7Ov": "2025-09-01",
      "01tTf000004ROju": "2026-09-01",
      "01tTf000004RRpp": "2026-09-01",
      "01tTf000004RRrR": "2026-03-01",
      "01tTf000004RRt3": "2026-03-01",
      "01t6700000E4lnC": "2025-09-01",
      "01t6700000E4lq5": "2025-09-01",
      "01t6700000E4lup": "2025-09-01",
      "01t6700000E4luu": "2025-09-01",
      "01t6700000E4luz": "2025-09-01",
      "01t6700000E4lv4": "2025-09-01",
      "01tTf000004RM5Q": "2026-03-01",
      "01tTf000004RPo2": "2026-09-01",
      "01tTf000004RSVm": "2026-03-01",
      "01tTf000004RSYz": "2026-09-01",
      "01tTf000004RSab": "2026-09-01",
      "01tTf000004RScD": "2026-09-01",
      "01tTf000004RSdp": "2026-03-01",
      "01tTf000004RSfR": "2026-09-01",
      "01tTf000004RSh3": "2026-09-01",
      "01tTf000004RSif": "2026-03-01",
      "01tTf000004RSkH": "2026-03-01",
      "01tTf000004RSlt": "2026-03-01",
      "01tTf000006MIqv": "2025-09-01",
      "01tTf000006MJII": "2025-09-01",
      "01tTf000006MJIJ": "2026-03-01",
      "01tTf000006MJN7": "2026-03-01",
      "01tTf000006MJN8": "2026-09-01",
      "01tTf000006MJOj": "2026-09-01",
      "01tTf0000028cRx": "2025-09-01",
      "01tTf000004ROLh": "2026-09-01",
      "01tTf00000575po": "2026-03-01",
      "01t6700000E4lmM": "2025-09-01",
      "01t6700000E4lzQ": "2025-09-01",
      "01tTf000004RNCk": "2026-09-01",
      "01tTf000004RO2L": "2026-09-01",
      "01tTf000004RO3x": "2026-03-01",
      "01tTf000004RO5Z": "2026-03-01",
      "01t6700000E4lm2": "2025-09-01",
      "01t6700000E4lzG": "2025-09-01",
      "01tTf000004RNnp": "2026-09-01",
      "01tTf000004RNpR": "2026-03-01",
      "01tTf000004RNr3": "2026-09-01",
      "01tTf000004RNsf": "2026-03-01",
      "01t6700000E4lmC": "2025-09-01",
      "01tTf000004RNz7": "2026-09-01",
      "01tTf000004RO0j": "2026-03-01",
      "01t6700000E4llT": "2025-09-01",
      "01tTf000004RNKn": "2026-09-01",
      "01tTf000004RNMP": "2026-03-01",
      "01t6700000E4lzL": "2025-09-01",
      "01tTf000004RLdz": "2026-03-01",
      "01tTf000004RNxV": "2026-09-01",
      "01t6700000E4lln": "2025-09-01",
      "01t6700000E4lls": "2025-09-01",
      "01tTf000004RKmk": "2026-09-01",
      "01tTf000004RNe9": "2026-03-01",
      "01tTf000004RNfl": "2026-09-01",
      "01tTf000004RNhN": "2026-03-01",
      "01t6700000E4llY": "2025-09-01",
      "01tTf000004RNPd": "2026-09-01",
      "01tTf000004RNRF": "2026-03-01",
      "01t6700000E4lld": "2025-09-01",
      "01tTf000004RNSr": "2026-09-01",
      "01tTf000004RNW5": "2026-03-01",
      "01t6700000E4llO": "2025-09-01",
      "01tTf000004RNEL": "2026-09-01",
      "01tTf000004RNFx": "2026-03-01",
      "01t6700000E4lli": "2025-09-01",
      "01tTf000004RNXh": "2026-09-01",
      "01tTf000004RNav": "2026-03-01",
      "01t6700000E4lzB": "2025-09-01",
      "01tTf000004RLEA": "2026-03-01",
      "01tTf000004RNcX": "2026-09-01",
      "01t6700000E4lvJ": "2025-09-01",
      "01t6700000E4lvO": "2025-09-01",
      "01t6700000E4lvT": "2025-09-01",
      "01tTf000004RLxK": "2026-09-01",
      "01tTf000004RPMe": "2026-09-01",
      "01tTf000004RSqj": "2026-03-01",
      "01tTf000004RSsL": "2026-03-01",
      "01tTf000004RStx": "2026-03-01",
      "01tTf000004RSvZ": "2026-09-01",
      "01t6700000E4ltT": "2025-09-01",
      "01t6700000E4lth": "2025-09-01",
      "01t6700000E4ltm": "2025-09-01",
      "01tTf000004RLu7": "2026-09-01",
      "01tTf000004RMbe": "2026-03-01",
      "01tTf000004RQ5m": "2026-09-01",
      "01tTf000004RQXC": "2026-03-01",
      "01tTf000004RRzV": "2026-03-01",
      "01tTf000004RS17": "2026-09-01",
      "01t6700000E4lnD": "2025-09-01",
      "01t6700000E4lv9": "2025-09-01",
      "01t6700000E4lvE": "2025-09-01",
      "01tTf000004RNsg": "2026-09-01",
      "01tTf000004ROGt": "2026-03-01",
      "01tTf000004RPRT": "2026-09-01",
      "01tTf000004RRBX": "2026-03-01",
      "01tTf000004RSnV": "2026-03-01",
      "01tTf000004RSp7": "2026-09-01",
      "01t6700000E4lm7": "2025-09-01",
      "01tTf000004RNuH": "2026-09-01",
      "01tTf000004RNvt": "2026-03-01",
      "01t6700000E4lrS": "2025-09-01",
      "01t6700000E4lsK": "2025-09-01",
      "01tTf000004RMod": "2026-09-01",
      "01tTf000004RR8H": "2026-03-01",
      "01tTf000004RR9t": "2026-03-01",
      "01tTf000004RRBV": "2026-09-01",
      "01t6700000E4lkg": "2025-09-01",
      "01t6700000E4lsj": "2025-09-01",
      "01tTf000004RL66": "2026-09-01",
      "01tTf000004ROdT": "2026-03-01",
      "01tTf000004RRRd": "2026-03-01",
      "01tTf000004RRTF": "2026-09-01",
      "01t6700000E4lso": "2025-09-01",
      "01t6700000E4lst": "2025-09-01",
      "01tTf000004RLR4": "2026-09-01",
      "01tTf000004RMwc": "2026-03-01",
      "01tTf000004RRUr": "2026-03-01",
      "01tTf000004RRWT": "2026-09-01",
      "01t6700000E4lsB": "2025-09-01",
      "01tTf000004RLnf": "2026-03-01",
      "01tTf000004RRLB": "2026-09-01",
      "01t6700000E4lrw": "2025-09-01",
      "01t6700000E4lsy": "2025-09-01",
      "01tTf000004RMgU": "2026-03-01",
      "01tTf000004RRY5": "2026-09-01",
      "01tTf000004RRZh": "2026-03-01",
      "01tTf000004RRbJ": "2026-09-01",
      "01t6700000E4lrx": "2025-09-01",
      "01t6700000E4ltS": "2025-09-01",
      "01tTf000004ROYc": "2026-03-01",
      "01tTf000004RReY": "2026-09-01",
      "01tTf000004RRkz": "2026-03-01",
      "01tTf000004RRmb": "2026-09-01",
      "01t6700000E4ltI": "2025-09-01",
      "01t6700000E4ltN": "2025-09-01",
      "01tTf000004RQqY": "2026-03-01",
      "01tTf000004RRhl": "2026-09-01",
      "01t6700000E4llt": "2025-09-01",
      "01t6700000E4lsP": "2025-09-01",
      "01tTf000004RLhC": "2026-09-01",
      "01tTf000004RMRz": "2026-03-01",
      "01tTf000004RRGL": "2026-09-01",
      "01tTf000004RRHx": "2026-03-01",
      "01t6700000E4lsZ": "2025-09-01",
      "01t6700000E4lse": "2025-09-01",
      "01tTf000004RLhD": "2026-03-01",
      "01tTf000004RPMd": "2026-09-01",
      "01tTf000004RRMn": "2026-03-01",
      "01tTf000004RROP": "2026-09-01",
      "01t6700000E4lsU": "2025-09-01",
      "01tTf000004ROVO": "2026-03-01",
      "01tTf000004RPL0": "2026-09-01",
      "01t6700000E4ltX": "2025-09-01",
      "01tTf000004RKTP": "2026-03-01",
      "01tTf000004RRwH": "2026-09-01",
      "01t6700000E4lsA": "2025-09-01",
      "01tTf000004RKy6": "2026-09-01",
      "01tTf000004RR6f": "2026-03-01",
      "01t6700000E4lvY": "2025-09-01",
      "01t6700000E4lvd": "2025-09-01",
      "01t6700000E4lvi": "2025-09-01",
      "01tTf000004RIeU": "2026-03-01",
      "01tTf000004RKRn": "2026-03-01",
      "01tTf000004RLXX": "2026-03-01",
      "01tTf000004RMGg": "2026-09-01",
      "01tTf000004RSxB": "2026-09-01",
      "01tTf000004RSyn": "2026-09-01",
      "01tTf00000270YR": "2025-09-01",
      "01tTf0000028bUI": "2025-09-01",
      "01tTf0000028cWn": "2025-09-01",
      "01tTf000004RO8o": "2026-09-01",
      "01tTf000004RQ5l": "2026-03-01",
      "01tTf000004RQ7N": "2026-03-01",
      "01tTf000004RQ8z": "2026-03-01",
      "01tTf000004RQAb": "2026-09-01",
      "01tTf000004RQCD": "2026-09-01",
      "01tTf0000028cYP": "2025-09-01",
      "01tTf0000028ca1": "2025-09-01",
      "01tTf0000028cbd": "2025-09-01",
      "01tTf000004RJCT": "2026-09-01",
      "01tTf000004RNfm": "2026-03-01",
      "01tTf000004RQDp": "2026-03-01",
      "01tTf000004RQFR": "2026-03-01",
      "01tTf000004RQH3": "2026-09-01",
      "01tTf000004RQIf": "2026-09-01",
      "01t6700000E4lnt": "2025-09-01",
      "01t6700000E4lny": "2025-09-01",
      "01tTf000004ROYb": "2026-03-01",
      "01tTf000004RObp": "2026-03-01",
      "01tTf000004ROf3": "2026-09-01",
      "01tTf000004ROgf": "2026-09-01",
      "01t6700000E4lmW": "2025-09-01",
      "01tTf000004RO8n": "2026-03-01",
      "01tTf000004ROAP": "2026-09-01",
      "01tTf000006M50o": "2026-03-01",
      "01tTf000006M50p": "2026-09-01",
      "01tTf000006MFmZ": "2025-09-01",
      "01t6700000E4llx": "2025-09-01",
      "01tTf000004RNkb": "2026-03-01",
      "01tTf000004RNmD": "2026-09-01",
      "01t6700000E4oug": "2025-09-01",
      "01tTf000004RNHZ": "2026-03-01",
      "01tTf000004RNJB": "2026-09-01",
      "01tTf000002Hk71": "2025-09-01",
      "01tTf000002Hk72": "2025-09-01",
      "01tTf000002Hk73": "2025-09-01",
      "01tTf000004ROOw": "2026-03-01",
      "01tTf000004RSIr": "2026-03-01",
      "01tTf000004RSKT": "2026-03-01",
      "01tTf000004RSNh": "2026-09-01",
      "01tTf000004RSPJ": "2026-09-01",
      "01tTf000004RSQv": "2026-09-01",
      "01tTf000002H6Px": "2025-09-01",
      "01tTf000004RJyk": "2026-03-01",
      "01tTf000004RPT3": "2026-09-01",
      "01tTf000003BRkl": "2025-09-01",
      "01tTf000003BRkm": "2025-09-01",
      "01tTf000003BRkn": "2025-09-01",
      "01tTf000004RNB9": "2026-09-01",
      "01tTf000004RQKH": "2026-03-01",
      "01tTf000004RQLt": "2026-03-01",
      "01tTf000004RQNV": "2026-03-01",
      "01tTf000004RQP7": "2026-09-01",
      "01tTf000004RQQj": "2026-09-01",
      "01tTf000003Szzu": "2025-09-01",
      "01tTf000003Szzv": "2026-01-01",
      "01tTf000004RTI9": "2026-06-01",
      "01tTf000003Szzo": "2025-09-01",
      "01tTf000003Szzp": "2026-01-01",
      "01tTf000004RTJl": "2026-06-01",
      "01tTf000003T000": "2025-09-01",
      "01tTf000003T001": "2026-01-01",
      "01tTf000004LJ3l": "2025-09-01",
      "01tTf000004LJ8b": "2026-03-01",
      "01tTf000004iBa9": "2026-09-01",
      "01tTf000004QRiE": "2026-03-01",
      "01tTf000004QS9d": "2025-09-01",
      "01tTf000004iBVJ": "2026-09-01",
      "01tTf000004QS4n": "2025-09-01",
      "01tTf000004QS6P": "2026-03-01",
      "01tTf000004iBYX": "2026-09-01",
      "01tTf000004VNcL": "2025-09-01",
      "01tTf000004iBof": "2026-09-01",
      "01tTf000005770K": "2026-03-01",
      "01tTf000004QSJJ": "2025-09-01",
      "01tTf000004i7Oi": "2026-09-01",
      "01tTf00000575pq": "2026-03-01",
      "01tTf000004QRyL": "2025-09-01",
      "01tTf000004QRzx": "2026-03-01",
      "01tTf000004iBTh": "2026-09-01",
      "01tTf000004QSET": "2025-09-01",
      "01tTf000004QSG5": "2026-03-01",
      "01tTf000004iBWv": "2026-09-01",
      "01tTf000004RJNe": "2026-03-01",
      "01tTf000004RM5N": "2025-09-01",
      "01tTf000004RM6z": "2026-09-01",
      "01tTf000006Q3rd": "2025-09-01",
      "01tTf000006Q3re": "2026-03-01",
      "01tTf000006Q3tF": "2026-09-01",
      "01tTf000006Q3tG": "2025-09-01",
      "01tTf000006Q3ur": "2026-03-01",
      "01tTf000006Q3us": "2026-09-01",
      "01tTf000006Q3N0": "2026-03-01",
      "01tTf000006Q3N1": "2026-09-01",
      "01tTf000006Q3q2": "2025-09-01",
      "01tTf000006Q2qk": "2026-09-01",
      "01tTf000006Q3wT": "2025-09-01",
      "01tTf000006Q3wU": "2026-03-01"
    },
    startTermDisplayName: {
      "01t6700000E4lmRAAR": "September 2025",
      "01tTf000004RLakIAG": "March 2026",
      "01tTf000004RO7BIAW": "September 2026",
      "01t6700000E4ln0AAB": "September 2025",
      "01tTf000004ROGrIAO": "September 2026",
      "01tTf000004ROITIA4": "March 2026",
      "01t6700000E4lnAAAR": "September 2025",
      "01tTf000004RNPeIAO": "March 2026",
      "01tTf000004RONJIA4": "September 2026",
      "01tTf000002S3eXIAS": "September 2025",
      "01tTf000004ROQXIA4": "March 2026",
      "01tTf000004ROS9IAO": "September 2026",
      "01tTf000002S3g9IAC": "September 2025",
      "01tTf000004ROTlIAO": "March 2026",
      "01tTf000004ROVNIA4": "September 2026",
      "01t6700000E4loDAAR": "September 2025",
      "01tTf000004RKmlIAG": "March 2026",
      "01tTf000004RMTaIAO": "September 2026",
      "01tTf000004RNB8IAO": "September 2026",
      "01tTf000004ROjtIAG": "September 2026",
      "01tTf000004ROlVIAW": "March 2026",
      "01tTf000004ROn7IAG": "March 2026",
      "01tTf000004ROojIAG": "March 2026",
      "01tTf000004ROqLIAW": "September 2026",
      "01tTf000004VICrIAO": "September 2025",
      "01tTf000004VICsIAO": "September 2025",
      "01tTf000004VIETIA4": "September 2025",
      "01t6700000E4lloAAB": "September 2025",
      "01t6700000E4loSAAR": "September 2025",
      "01tTf000004ROwnIAG": "September 2026",
      "01tTf000004ROyPIAW": "March 2026",
      "01tTf000004RP1dIAG": "September 2026",
      "01tTf000004RP4rIAG": "March 2026",
      "01t6700000E4lorAAB": "September 2025",
      "01t6700000E4lzkAAB": "September 2025",
      "01tTf000004RLUIIA4": "March 2026",
      "01tTf000004RPG9IAO": "September 2026",
      "01tTf000004RPHlIAO": "September 2026",
      "01tTf000004RPJNIA4": "March 2026",
      "01t6700000E4lohAAB": "September 2025",
      "01t6700000E4lomAAB": "September 2025",
      "01t6700000E4lzfAAB": "September 2025",
      "01tTf000004RNeAIAW": "September 2026",
      "01tTf000004RP6TIAW": "March 2026",
      "01tTf000004RP85IAG": "September 2026",
      "01tTf000004RP9hIAG": "March 2026",
      "01tTf000004RPBJIA4": "September 2026",
      "01tTf000004RPCvIAO": "March 2026",
      "01t6700000E4lpfAAB": "September 2025",
      "01t6700000E4lpkAAB": "September 2025",
      "01t6700000E4lppAAB": "September 2025",
      "01tTf000004RPjBIAW": "September 2026",
      "01tTf000004RPknIAG": "March 2026",
      "01tTf000004RPo1IAG": "September 2026",
      "01tTf000004RPpdIAG": "March 2026",
      "01tTf000004RPrFIAW": "September 2026",
      "01tTf000004RPsrIAG": "March 2026",
      "01tTf000006MF3OIAW": "September 2025",
      "01tTf000006MF3PIAW": "March 2026",
      "01tTf000006MFmYIAW": "September 2026",
      "01t6700000E4lwvAAB": "September 2025",
      "01t6700000E4lx0AAB": "September 2025",
      "01t6700000E4lx5AAB": "September 2025",
      "01tTf000004VJiPIAW": "September 2026",
      "01tTf000004VJk1IAG": "September 2026",
      "01tTf000004VJldIAG": "September 2026",
      "01tTf000004VJthIAG": "March 2026",
      "01tTf000004VJvJIAW": "March 2026",
      "01tTf000004VJwvIAG": "March 2026",
      "01t6700000E4lxFAAR": "September 2025",
      "01t6700000E4lxKAAR": "September 2025",
      "01tTf000004VJorIAG": "September 2026",
      "01tTf000004VJqTIAW": "September 2026",
      "01t6700000E4lqTAAR": "September 2025",
      "01t6700000E4lqYAAR": "September 2025",
      "01t6700000E4lqdAAB": "September 2025",
      "01tTf000004RPMcIAO": "September 2026",
      "01tTf000004RPmQIAW": "September 2026",
      "01tTf000004RQYnIAO": "September 2026",
      "01tTf000004RQaPIAW": "March 2026",
      "01tTf000004RQc1IAG": "March 2026",
      "01tTf000004RQddIAG": "March 2026",
      "01t6700000E4lqiAAB": "September 2025",
      "01t6700000E4lqnAAB": "September 2025",
      "01t6700000E4lqsAAB": "September 2025",
      "01tTf000004RM5PIAW": "September 2026",
      "01tTf000004RO2MIAW": "September 2026",
      "01tTf000004ROdSIAW": "March 2026",
      "01tTf000004RQfFIAW": "September 2026",
      "01tTf000004RQgrIAG": "March 2026",
      "01tTf000004RQiTIAW": "March 2026",
      "01t6700000E4lrHAAR": "September 2025",
      "01t6700000E4lrMAAR": "September 2025",
      "01t6700000E4lrRAAR": "September 2025",
      "01tTf000004RM70IAG": "March 2026",
      "01tTf000004RQk5IAG": "September 2026",
      "01tTf000004RQlhIAG": "September 2026",
      "01tTf000004RQnJIAW": "September 2026",
      "01tTf000004RQovIAG": "March 2026",
      "01tTf000004RQs9IAG": "March 2026",
      "01t6700000E4lrIAAR": "September 2025",
      "01t6700000E4lrvAAB": "September 2025",
      "01t6700000E4m0JAAR": "September 2025",
      "01t6700000E4m0OAAR": "September 2025",
      "01t6700000E4m0TAAR": "September 2025",
      "01tTf000004RKTOIA4": "March 2026",
      "01tTf000004RLcOIAW": "September 2026",
      "01tTf000004RMtPIAW": "September 2026",
      "01tTf000004RNZKIA4": "March 2026",
      "01tTf000004RNfnIAG": "March 2026",
      "01tTf000004RQybIAG": "September 2026",
      "01tTf000004RR0DIAW": "September 2026",
      "01tTf000004RR1pIAG": "September 2026",
      "01tTf000004RR3RIAW": "March 2026",
      "01tTf000004RR53IAG": "March 2026",
      "01t6700000E4ltrAAB": "September 2025",
      "01t6700000E4ltwAAB": "September 2025",
      "01t6700000E4lu1AAB": "September 2025",
      "01tTf000004RNuIIAW": "September 2026",
      "01tTf000004RQdfIAG": "September 2026",
      "01tTf000004RS4LIAW": "September 2026",
      "01tTf000004RS7ZIAW": "March 2026",
      "01tTf000004RS9BIAW": "March 2026",
      "01tTf000004RSAnIAO": "March 2026",
      "01t6700000E4lwgAAB": "September 2025",
      "01t6700000E4lwlAAB": "September 2025",
      "01t6700000E4lwqAAB": "September 2025",
      "01tTf000003fORHIA2": "September 2025",
      "01tTf000003fORIIA2": "March 2026",
      "01tTf000004RKI6IAO": "September 2026",
      "01tTf000004ROX0IAO": "March 2026",
      "01tTf000004RP6UIAW": "March 2026",
      "01tTf000004RTIAIA4": "September 2026",
      "01tTf000004RTLNIA4": "September 2026",
      "01tTf000004RTMzIAO": "March 2026",
      "01tTf000004RTObIAO": "September 2026",
      "01t6700000E4ln5AAB": "September 2025",
      "01tTf000004RNHaIAO": "September 2026",
      "01tTf000004ROK5IAO": "March 2026",
      "01t6700000E4lnFAAR": "September 2025",
      "01tTf000004RM5OIAW": "September 2026",
      "01tTf000004ROOvIAO": "March 2026",
      "01t6700000E4lpLAAR": "September 2025",
      "01t6700000E4lpQAAR": "September 2025",
      "01tTf000004RJKWIA4": "September 2026",
      "01tTf000004RK3aIAG": "March 2026",
      "01tTf000004RPUfIAO": "September 2026",
      "01tTf000004RPWHIA4": "March 2026",
      "01t6700000E4lrWAAR": "September 2025",
      "01t6700000E4lrbAAB": "September 2025",
      "01t6700000E4lrgAAB": "September 2025",
      "01tTf000004RI0AIAW": "March 2026",
      "01tTf000004RPRSIA4": "March 2026",
      "01tTf000004RQYoIAO": "September 2026",
      "01tTf000004RQtlIAG": "September 2026",
      "01tTf000004RQvNIAW": "September 2026",
      "01tTf000004RQwzIAG": "March 2026",
      "01t6700000E4lt3AAB": "September 2025",
      "01t6700000E4lt8AAB": "September 2025",
      "01t6700000E4ltDAAR": "September 2025",
      "01tTf000004RLu6IAG": "July 2026",
      "01tTf000004RReXIAW": "January 2027",
      "01tUC000008Cj1aYAC": "January 2026",
      "01t6700000E4lqJAAR": "September 2025",
      "01t6700000E4lqOAAR": "September 2025",
      "01t6700000E4lzMAAR": "September 2025",
      "01t6700000E4m09AAB": "September 2025",
      "01t6700000E4m0EAAR": "September 2025",
      "01tTf000004RJylIAG": "March 2026",
      "01tTf000004RKy5IAG": "March 2026",
      "01tTf000004RLpGIAW": "March 2026",
      "01tTf000004RMIIIA4": "September 2026",
      "01tTf000004RMocIAG": "September 2026",
      "01tTf000004RNeBIAW": "September 2026",
      "01tTf000004RQSLIA4": "September 2026",
      "01tTf000004RQTxIAO": "March 2026",
      "01tTf000004RQVZIA4": "September 2026",
      "01tTf000004RQXBIA4": "March 2026",
      "01t6700000E4lzVAAR": "September 2025",
      "01tTf000004ROC1IAO": "September 2026",
      "01tTf000004ROFFIA4": "March 2026",
      "01t6700000E4lzpAAB": "September 2025",
      "01tTf000004RPb7IAG": "September 2026",
      "01tTf000004RPcjIAG": "March 2026",
      "01tTf000006MFuhIAG": "September 2025",
      "01tTf000006MFuiIAG": "March 2026",
      "01tTf000006MJLVIA4": "September 2026",
      "01t6700000E4luGAAR": "September 2025",
      "01t6700000E4luLAAR": "September 2025",
      "01t6700000E4m0dAAB": "September 2025",
      "01t6700000E4m0iAAB": "September 2025",
      "01t6700000E4m0nAAB": "September 2025",
      "01tTf000004RK8QIAW": "September 2026",
      "01tTf000004RKN3IAO": "September 2026",
      "01tTf000004RMgVIAW": "September 2026",
      "01tTf000004RPJOIA4": "March 2026",
      "01tTf000004RRWUIA4": "March 2026",
      "01tTf000004RRxuIAG": "March 2026",
      "01tTf000004RSCPIA4": "September 2026",
      "01tTf000004RSE1IAO": "March 2026",
      "01tTf000004RSFdIAO": "March 2026",
      "01tTf000004RSHFIA4": "September 2026",
      "01t6700000E4lzaAAB": "September 2025",
      "01tTf000004ROGsIAO": "September 2026",
      "01tTf000004ROWzIAO": "March 2026",
      "01t6700000E4lqFAAR": "September 2025",
      "01t6700000E4lwMAAR": "September 2025",
      "01t6700000E4lwRAAR": "September 2025",
      "01t6700000E4m0sAAB": "September 2025",
      "01t6700000E4m0xAAB": "September 2025",
      "01tTf000004RLFmIAO": "September 2026",
      "01tTf000004RLalIAG": "March 2026",
      "01tTf000004RNhOIAW": "September 2026",
      "01tTf000004RPo3IAG": "September 2026",
      "01tTf000004RQqZIAW": "March 2026",
      "01tTf000004RTA5IAO": "September 2026",
      "01tTf000004RTBhIAO": "September 2026",
      "01tTf000004RTDJIA4": "March 2026",
      "01tTf000004RTEvIAO": "March 2026",
      "01tTf000004RTGXIA4": "March 2026",
      "01t6700000B8lZ8AAJ": "March 2026",
      "01t6700000E5uVBAAZ": "March 2026",
      "01t6700000E5uVGAAZ": "March 2026",
      "01t6700000E5uVLAAZ": "March 2026",
      "01t6700000E5uVQAAZ": "March 2026",
      "01t6700000E4lqZAAR": "September 2025",
      "01t6700000E4lw2AAB": "September 2025",
      "01tTf000004RRjOIAW": "September 2026",
      "01tTf000004RT5FIAW": "March 2026",
      "01tTf000004RT6rIAG": "September 2026",
      "01tTf000004RT8TIAW": "March 2026",
      "01t6700000E4lvsAAB": "September 2025",
      "01t6700000E4lvxAAB": "September 2025",
      "01tTf000004RNJCIA4": "March 2026",
      "01tTf000004RPXuIAO": "September 2026",
      "01tTf000004RT21IAG": "September 2026",
      "01tTf000004RT3dIAG": "March 2026",
      "01t6700000E4lpuAAB": "September 2025",
      "01t6700000E4lpzAAB": "September 2025",
      "01tTf000004RJ7dIAG": "September 2026",
      "01tTf000004RPuTIAW": "September 2026",
      "01tTf000004RPw5IAG": "March 2026",
      "01tTf000004h4UIIAY": "March 2026",
      "01t6700000E4loOAAR": "September 2025",
      "01t6700000E4lpBAAR": "September 2025",
      "01tTf000004RNFyIAO": "March 2026",
      "01tTf000004RPKzIAO": "September 2026",
      "01tTf000004RPMbIAO": "March 2026",
      "01tTf000004RPODIA4": "September 2026",
      "01t6700000DCCwrAAH": "September 2025",
      "01tTf000004RPfxIAG": "September 2026",
      "01tTf000005770IIAQ": "March 2026",
      "01t6700000E4loIAAR": "September 2025",
      "01t6700000E4loNAAR": "September 2025",
      "01tTf000004ROtZIAW": "September 2026",
      "01tTf000004ROvBIAW": "September 2026",
      "01t6700000E4lmhAAB": "September 2025",
      "01t6700000E4lqEAAR": "September 2025",
      "01tTf000004RKtCIAW": "September 2026",
      "01tTf000004RL1GIAW": "March 2026",
      "01tTf000004RLkQIAW": "March 2026",
      "01tTf000004RQ0wIAG": "September 2026",
      "01tTf000004RQ2XIAW": "March 2026",
      "01tTf000004RQ49IAG": "September 2026",
      "01tTf000004VIEUIA4": "September 2025",
      "01t6700000E4lsFAAR": "September 2025",
      "01tTf000002H7iHIAS": "September 2025",
      "01tTf000004RN7uIAG": "September 2026",
      "01tTf000004RNmFIAW": "March 2026",
      "01tTf000004RPCwIAO": "March 2026",
      "01tTf000004RQdeIAG": "September 2026",
      "01t6700000E4m0YAAR": "September 2025",
      "01tTf000004RKYEIA4": "September 2026",
      "01tTf000004RNmEIAW": "March 2026",
      "01t6700000E4ltcAAB": "September 2025",
      "01tTf000004RRLCIA4": "March 2026",
      "01tTf000004RRxtIAG": "September 2026",
      "01t6700000E4loxAAB": "September 2025",
      "01tTf000002H7OvIAK": "September 2025",
      "01tTf000004ROjuIAG": "September 2026",
      "01tTf000004RRppIAG": "September 2026",
      "01tTf000004RRrRIAW": "March 2026",
      "01tTf000004RRt3IAG": "March 2026",
      "01t6700000E4lnCAAR": "September 2025",
      "01t6700000E4lq5AAB": "September 2025",
      "01t6700000E4lupAAB": "September 2025",
      "01t6700000E4luuAAB": "September 2025",
      "01t6700000E4luzAAB": "September 2025",
      "01t6700000E4lv4AAB": "September 2025",
      "01tTf000004RM5QIAW": "March 2026",
      "01tTf000004RPo2IAG": "September 2026",
      "01tTf000004RSVmIAO": "March 2026",
      "01tTf000004RSYzIAO": "September 2026",
      "01tTf000004RSabIAG": "September 2026",
      "01tTf000004RScDIAW": "September 2026",
      "01tTf000004RSdpIAG": "March 2026",
      "01tTf000004RSfRIAW": "September 2026",
      "01tTf000004RSh3IAG": "September 2026",
      "01tTf000004RSifIAG": "March 2026",
      "01tTf000004RSkHIAW": "March 2026",
      "01tTf000004RSltIAG": "March 2026",
      "01tTf000006MIqvIAG": "September 2025",
      "01tTf000006MJIIIA4": "September 2025",
      "01tTf000006MJIJIA4": "March 2026",
      "01tTf000006MJN7IAO": "March 2026",
      "01tTf000006MJN8IAO": "September 2026",
      "01tTf000006MJOjIAO": "September 2026",
      "01tTf0000028cRxIAI": "September 2025",
      "01tTf000004ROLhIAO": "September 2026",
      "01tTf00000575poIAA": "March 2026",
      "01t6700000E4lmMAAR": "September 2025",
      "01t6700000E4lzQAAR": "September 2025",
      "01tTf000004RNCkIAO": "September 2026",
      "01tTf000004RO2LIAW": "September 2026",
      "01tTf000004RO3xIAG": "March 2026",
      "01tTf000004RO5ZIAW": "March 2026",
      "01t6700000E4lm2AAB": "September 2025",
      "01t6700000E4lzGAAR": "September 2025",
      "01tTf000004RNnpIAG": "September 2026",
      "01tTf000004RNpRIAW": "March 2026",
      "01tTf000004RNr3IAG": "September 2026",
      "01tTf000004RNsfIAG": "March 2026",
      "01t6700000E4lmCAAR": "September 2025",
      "01tTf000004RNz7IAG": "September 2026",
      "01tTf000004RO0jIAG": "March 2026",
      "01t6700000E4llTAAR": "September 2025",
      "01tTf000004RNKnIAO": "September 2026",
      "01tTf000004RNMPIA4": "March 2026",
      "01t6700000E4lzLAAR": "September 2025",
      "01tTf000004RLdzIAG": "March 2026",
      "01tTf000004RNxVIAW": "September 2026",
      "01t6700000E4llnAAB": "September 2025",
      "01t6700000E4llsAAB": "September 2025",
      "01tTf000004RKmkIAG": "September 2026",
      "01tTf000004RNe9IAG": "March 2026",
      "01tTf000004RNflIAG": "September 2026",
      "01tTf000004RNhNIAW": "March 2026",
      "01t6700000E4llYAAR": "September 2025",
      "01tTf000004RNPdIAO": "September 2026",
      "01tTf000004RNRFIA4": "March 2026",
      "01t6700000E4lldAAB": "September 2025",
      "01tTf000004RNSrIAO": "September 2026",
      "01tTf000004RNW5IAO": "March 2026",
      "01t6700000E4llOAAR": "September 2025",
      "01tTf000004RNELIA4": "September 2026",
      "01tTf000004RNFxIAO": "March 2026",
      "01t6700000E4lliAAB": "September 2025",
      "01tTf000004RNXhIAO": "September 2026",
      "01tTf000004RNavIAG": "March 2026",
      "01t6700000E4lzBAAR": "September 2025",
      "01tTf000004RLEAIA4": "March 2026",
      "01tTf000004RNcXIAW": "September 2026",
      "01t6700000E4lvJAAR": "September 2025",
      "01t6700000E4lvOAAR": "September 2025",
      "01t6700000E4lvTAAR": "September 2025",
      "01tTf000004RLxKIAW": "September 2026",
      "01tTf000004RPMeIAO": "September 2026",
      "01tTf000004RSqjIAG": "March 2026",
      "01tTf000004RSsLIAW": "March 2026",
      "01tTf000004RStxIAG": "March 2026",
      "01tTf000004RSvZIAW": "September 2026",
      "01t6700000E4ltTAAR": "September 2025",
      "01t6700000E4lthAAB": "September 2025",
      "01t6700000E4ltmAAB": "September 2025",
      "01tTf000004RLu7IAG": "September 2026",
      "01tTf000004RMbeIAG": "March 2026",
      "01tTf000004RQ5mIAG": "September 2026",
      "01tTf000004RQXCIA4": "March 2026",
      "01tTf000004RRzVIAW": "March 2026",
      "01tTf000004RS17IAG": "September 2026",
      "01t6700000E4lnDAAR": "September 2025",
      "01t6700000E4lv9AAB": "September 2025",
      "01t6700000E4lvEAAR": "September 2025",
      "01tTf000004RNsgIAG": "September 2026",
      "01tTf000004ROGtIAO": "March 2026",
      "01tTf000004RPRTIA4": "September 2026",
      "01tTf000004RRBXIA4": "March 2026",
      "01tTf000004RSnVIAW": "March 2026",
      "01tTf000004RSp7IAG": "September 2026",
      "01t6700000E4lm7AAB": "September 2025",
      "01tTf000004RNuHIAW": "September 2026",
      "01tTf000004RNvtIAG": "March 2026",
      "01t6700000E4lrSAAR": "September 2025",
      "01t6700000E4lsKAAR": "September 2025",
      "01tTf000004RModIAG": "September 2026",
      "01tTf000004RR8HIAW": "March 2026",
      "01tTf000004RR9tIAG": "March 2026",
      "01tTf000004RRBVIA4": "September 2026",
      "01t6700000E4lkgAAB": "September 2025",
      "01t6700000E4lsjAAB": "September 2025",
      "01tTf000004RL66IAG": "September 2026",
      "01tTf000004ROdTIAW": "March 2026",
      "01tTf000004RRRdIAO": "March 2026",
      "01tTf000004RRTFIA4": "September 2026",
      "01t6700000E4lsoAAB": "September 2025",
      "01t6700000E4lstAAB": "September 2025",
      "01tTf000004RLR4IAO": "September 2026",
      "01tTf000004RMwcIAG": "March 2026",
      "01tTf000004RRUrIAO": "March 2026",
      "01tTf000004RRWTIA4": "September 2026",
      "01t6700000E4lsBAAR": "September 2025",
      "01tTf000004RLnfIAG": "March 2026",
      "01tTf000004RRLBIA4": "September 2026",
      "01t6700000E4lrwAAB": "September 2025",
      "01t6700000E4lsyAAB": "September 2025",
      "01tTf000004RMgUIAW": "March 2026",
      "01tTf000004RRY5IAO": "September 2026",
      "01tTf000004RRZhIAO": "March 2026",
      "01tTf000004RRbJIAW": "September 2026",
      "01t6700000E4lrxAAB": "September 2025",
      "01t6700000E4ltSAAR": "September 2025",
      "01tTf000004ROYcIAO": "March 2026",
      "01tTf000004RReYIAW": "September 2026",
      "01tTf000004RRkzIAG": "March 2026",
      "01tTf000004RRmbIAG": "September 2026",
      "01t6700000E4ltIAAR": "September 2025",
      "01t6700000E4ltNAAR": "September 2025",
      "01tTf000004RQqYIAW": "March 2026",
      "01tTf000004RRhlIAG": "September 2026",
      "01t6700000E4lltAAB": "September 2025",
      "01t6700000E4lsPAAR": "September 2025",
      "01tTf000004RLhCIAW": "September 2026",
      "01tTf000004RMRzIAO": "March 2026",
      "01tTf000004RRGLIA4": "September 2026",
      "01tTf000004RRHxIAO": "March 2026",
      "01t6700000E4lsZAAR": "September 2025",
      "01t6700000E4lseAAB": "September 2025",
      "01tTf000004RLhDIAW": "March 2026",
      "01tTf000004RPMdIAO": "September 2026",
      "01tTf000004RRMnIAO": "March 2026",
      "01tTf000004RROPIA4": "September 2026",
      "01t6700000E4lsUAAR": "September 2025",
      "01tTf000004ROVOIA4": "March 2026",
      "01tTf000004RPL0IAO": "September 2026",
      "01t6700000E4ltXAAR": "September 2025",
      "01tTf000004RKTPIA4": "March 2026",
      "01tTf000004RRwHIAW": "September 2026",
      "01t6700000E4lsAAAR": "September 2025",
      "01tTf000004RKy6IAG": "September 2026",
      "01tTf000004RR6fIAG": "March 2026",
      "01t6700000E4lvYAAR": "September 2025",
      "01t6700000E4lvdAAB": "September 2025",
      "01t6700000E4lviAAB": "September 2025",
      "01tTf000004RIeUIAW": "March 2026",
      "01tTf000004RKRnIAO": "March 2026",
      "01tTf000004RLXXIA4": "March 2026",
      "01tTf000004RMGgIAO": "September 2026",
      "01tTf000004RSxBIAW": "September 2026",
      "01tTf000004RSynIAG": "September 2026",
      "01tTf00000270YRIAY": "September 2025",
      "01tTf0000028bUIIAY": "September 2025",
      "01tTf0000028cWnIAI": "September 2025",
      "01tTf000004RO8oIAG": "September 2026",
      "01tTf000004RQ5lIAG": "March 2026",
      "01tTf000004RQ7NIAW": "March 2026",
      "01tTf000004RQ8zIAG": "March 2026",
      "01tTf000004RQAbIAO": "September 2026",
      "01tTf000004RQCDIA4": "September 2026",
      "01tTf0000028cYPIAY": "September 2025",
      "01tTf0000028ca1IAA": "September 2025",
      "01tTf0000028cbdIAA": "September 2025",
      "01tTf000004RJCTIA4": "September 2026",
      "01tTf000004RNfmIAG": "March 2026",
      "01tTf000004RQDpIAO": "March 2026",
      "01tTf000004RQFRIA4": "March 2026",
      "01tTf000004RQH3IAO": "September 2026",
      "01tTf000004RQIfIAO": "September 2026",
      "01t6700000E4lntAAB": "September 2025",
      "01t6700000E4lnyAAB": "September 2025",
      "01tTf000004ROYbIAO": "March 2026",
      "01tTf000004RObpIAG": "March 2026",
      "01tTf000004ROf3IAG": "September 2026",
      "01tTf000004ROgfIAG": "September 2026",
      "01t6700000E4lmWAAR": "September 2025",
      "01tTf000004RO8nIAG": "March 2026",
      "01tTf000004ROAPIA4": "September 2026",
      "01tTf000006M50oIAC": "March 2026",
      "01tTf000006M50pIAC": "September 2026",
      "01tTf000006MFmZIAW": "September 2025",
      "01t6700000E4llxAAB": "September 2025",
      "01tTf000004RNkbIAG": "March 2026",
      "01tTf000004RNmDIAW": "September 2026",
      "01t6700000E4ougAAB": "September 2025",
      "01tTf000004RNHZIA4": "March 2026",
      "01tTf000004RNJBIA4": "September 2026",
      "01tTf000002Hk71IAC": "September 2025",
      "01tTf000002Hk72IAC": "September 2025",
      "01tTf000002Hk73IAC": "September 2025",
      "01tTf000004ROOwIAO": "March 2026",
      "01tTf000004RSIrIAO": "March 2026",
      "01tTf000004RSKTIA4": "March 2026",
      "01tTf000004RSNhIAO": "September 2026",
      "01tTf000004RSPJIA4": "September 2026",
      "01tTf000004RSQvIAO": "September 2026",
      "01tTf000002H6PxIAK": "September 2025",
      "01tTf000004RJykIAG": "March 2026",
      "01tTf000004RPT3IAO": "September 2026",
      "01tTf000003BRklIAG": "September 2025",
      "01tTf000003BRkmIAG": "September 2025",
      "01tTf000003BRknIAG": "September 2025",
      "01tTf000004RNB9IAO": "September 2026",
      "01tTf000004RQKHIA4": "March 2026",
      "01tTf000004RQLtIAO": "March 2026",
      "01tTf000004RQNVIA4": "March 2026",
      "01tTf000004RQP7IAO": "September 2026",
      "01tTf000004RQQjIAO": "September 2026",
      "01tTf000003SzzuIAC": "September 2025",
      "01tTf000003SzzvIAC": "January 2026",
      "01tTf000004RTI9IAO": "June 2026",
      "01tTf000003SzzoIAC": "September 2025",
      "01tTf000003SzzpIAC": "January 2026",
      "01tTf000004RTJlIAO": "June 2026",
      "01tTf000003T000IAC": "September 2025",
      "01tTf000003T001IAC": "January 2026",
      "01tTf000004LJ3lIAG": "September 2025",
      "01tTf000004LJ8bIAG": "March 2026",
      "01tTf000004iBa9IAE": "September 2026",
      "01tTf000004QRiEIAW": "March 2026",
      "01tTf000004QS9dIAG": "September 2025",
      "01tTf000004iBVJIA2": "September 2026",
      "01tTf000004QS4nIAG": "September 2025",
      "01tTf000004QS6PIAW": "March 2026",
      "01tTf000004iBYXIA2": "September 2026",
      "01tTf000004VNcLIAW": "September 2025",
      "01tTf000004iBofIAE": "September 2026",
      "01tTf000005770KIAQ": "March 2026",
      "01tTf000004QSJJIA4": "September 2025",
      "01tTf000004i7OiIAI": "September 2026",
      "01tTf00000575pqIAA": "March 2026",
      "01tTf000004QRyLIAW": "September 2025",
      "01tTf000004QRzxIAG": "March 2026",
      "01tTf000004iBThIAM": "September 2026",
      "01tTf000004QSETIA4": "September 2025",
      "01tTf000004QSG5IAO": "March 2026",
      "01tTf000004iBWvIAM": "September 2026",
      "01tTf000004RJNeIAO": "March 2026",
      "01tTf000004RM5NIAW": "September 2025",
      "01tTf000004RM6zIAG": "September 2026",
      "01tTf000006Q3rdIAC": "September 2025",
      "01tTf000006Q3reIAC": "March 2026",
      "01tTf000006Q3tFIAS": "September 2026",
      "01tTf000006Q3tGIAS": "September 2025",
      "01tTf000006Q3urIAC": "March 2026",
      "01tTf000006Q3usIAC": "September 2026",
      "01tTf000006Q3N0IAK": "March 2026",
      "01tTf000006Q3N1IAK": "September 2026",
      "01tTf000006Q3q2IAC": "September 2025",
      "01tTf000006Q2qkIAC": "September 2026",
      "01tTf000006Q3wTIAS": "September 2025",
      "01tTf000006Q3wUIAS": "March 2026"
    },
    isPreviousHigherEducationEnrollment: {
      "1": "Yes",
      "0": "No"
    },
    isEnrolledInAnotherUniversity: {
      "1": "Yes",
      "0": "No"
    },
    isCorrespondanceAddressDiffer: {
      "1": "Yes",
      "0": "No"
    },
    educationDetailsEntryType: {
      "abroad": "Abroad",
      "german": "German",
      "graduation": "Graduation"
    },
    educationDetailsEntryTypeDisplayName: {
      "abroad": "Abroad",
      "german": "German",
      "graduation": "Graduation"
    }
  },

  transformationRules: {
    birthDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    submittedAt: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    examDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    firstEnrolmentDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    lastEnrolmentDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    dateOfEQHE: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    declaration1: {
      type: "boolean_conversion",
      true_values: ["1", "true", true],
      false_values: ["0", "false", false]
    },
    declaration2: {
      type: "boolean_conversion",
      true_values: ["1", "true", true],
      false_values: ["0", "false", false]
    },
    duration: {
      type: "type_conversion",
      target_type: "number"
    },
    programDurationDisplayName: {
      type: "type_conversion",
      target_type: "number"
    },
    userId: {
      type: "type_conversion",
      target_type: "string"
    },
    sfProgramId: {
      type: "type_conversion",
      target_type: "string"
    }
  }
};
