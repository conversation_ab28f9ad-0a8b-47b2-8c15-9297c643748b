export interface ApplicationMappingConfig {
  directFields: Record<string, string | string[]>;
  documentFields: Record<string, string | string[]>;
  arrayFields: Record<string, string | string[]>;
  subsectionMappings: Record<string, Record<string, string | string[]>>;
  picklistMappings: Record<string, Record<string, string>>;
  transformationRules: Record<string, any>;
}

export const UEG_APPLICATION_MAPPINGS: ApplicationMappingConfig = {
  directFields: {
    // Applicant Details
    "data.firstName": "firstName",
    "data.legalFamilyName": "lastName",
    "data.personalEmail": "email",
    "data.dateOfBirth": "birthDate",
    "data.placeOfBirth": "placeOfBirth",
    "data.countryOfBirth": "countryDisplayName",
    "data.citizenship": "citizenshipDisplayName",
    "data.correspondenceLanguage": ["correspondenceLanguage", "correspondenceLanguageDisplayName"],
    "data.applyReferFriendScheme": "isReferFriend",
    "data.referIdNumber": "ueIDNumber",
    "data.referFirstName": "referralFirstName",
    "data.referSurname": "referralLastName",
    "data.title": ["title", "titleDisplayName"],
    "data.phoneHome": "phoneNumber",
    "data.phone": "otherNumber",

    // Declaration & Data Protection
    "data.declarationInfoTrue": "informationAccuracyAgreement",
    "data.personalInfoTrue": "dataPrivacyConsent",

    // Higher Education
    "data.higherEducationExits": "isPreviousHigherEducationEnrollment",
    "data.educationMainCountry": "countryOfInitialRegistration",
    "data.educationMainGermanUniversity": ["universityOfInitialRegistration", "universityOfInitialRegistrationDisplayName"],
    "data.educationMainName": "otherUniversityName",
    "data.educationMainSemesterYearMonth": ["semesterOfInitialRegistration", "semesterOfInitialRegistrationDisplayName"],
    "data.educationAnotherUniversityEnrolledGermany": "isEnrolledInAnotherUniversity",

    // Correspondence Address
    "data.correspondenceStreetAddress": "correspondenceStreetAddress",
    "data.correspondenceCity": "correspondenceCity",
    "data.correspondenceState": "correspondenceState",
    "data.correspondenceCountry": "correspondenceCountryDisplayName",
    "data.correspondencePostalCode": "correspondencePostalCode",

    // Permanent Address
    "data.mailingAddress": "streetAddress",
    "data.city": "city",
    "data.country": "countryDisplayName",
    "data.state": [
      "mailingState",
      "mailingStateDisplayName"
    ],
    "data.postalCode": "postalCode",
    "data.addressCo": "careOf",
    "data.correspondenceAddress": "isCorrespondanceAddressDiffer",

    // Program Information
    "data.program": ["program", "programDisplayName"],
    "data.level": ["programTypeDisplayName", "programType"],
    "data.language": [
      "language",
      "languageDisplayName"
    ],
    "data.location": [
      "location",
      "locationDisplayName"
    ],
    "data.duration": [
      "duration",
      "programDurationDisplayName"
    ],
    "data.intake": "productId",

    // Application Metadata
    "uuid": "applicationId",
    "userId": "legacyUserId",
    "sfProgramId": "sfProgramId",
    "submittedAt": "submittedAt",
    "createdAt": "createdAt",
    "updatedAt": "updatedAt",
    "isSubmitted": "isSubmitted"
  },

  documentFields: {
    "data.fileCV": "CV",
    "data.photo": "photograph",
    "data.educationSchoolsCertificateFile": "HEEQ",
    "data.educationSchoolsTranscriptFile": "finalHEEQ",
    "data.fileGermanTest": "languageCertificateGermen",
    "data.fileEnglishTest": "languageCertificateEnglish",
    "data.fileEntranceTest": "entranceTest",
    "data.filePortfolio": "portfolio",
    "data.applyReasonFile": "motivationInformation",
    "data.nonObjectionCertificate": "noObjectionCertificate",
    "data.schoolLeavingCertificate": "deregistrationCertificate",
    "data.otherFiles": "otherCertificate",
    "data.fileEducationQualification": "bachelorTranscript"
  },

  arrayFields: {
    "data.educationSchools": "educationInfo",
    "data.eqhe": "eqheDetails"
  },

  subsectionMappings: {
    educationInfo: {
      "educationSchoolsType": ["educationDetailsEntryType", "educationDetailsEntryTypeDisplayName"],
      "educationSchoolsStudyStatus": ["educationStatus", "educationStatusDisplayName"],
      "educationSchoolsExamDate": "examDate",
      "educationSchoolsFirstEnrolmentYear": "firstEnrolmentDate",
      "educationSchoolsLastEnrolmentYear": "lastEnrolmentDate",
      "educationSchoolsSpecialisation": "programmeCompleted",
      "educationSchoolsDegree": ["degree", "degreeDisplayName"],
      "educationSchoolsSpecialisationName": ["specialisation", "specialisationDisplayName"],
      "educationSchoolsPartnerUniversity": ["partnerUniversityName", "partnerUniversityNameDisplayName"],
      "educationSchoolsPartnerUniversityOther": "otherPartnerUniversityName",
      "educationSchoolsUniversity": ["universityName", "universityNameDisplayName"],
      "educationSchoolsPresenceOrDistanceType": ["attendanceType", "attendanceTypeDisplayName"],
      "educationSchoolsStudyTimeType": ["studyMode", "studyModeDisplayName"],
      "educationSchoolsGrade": ["finalGrade", "finalGradeDisplayName"],
      "educationSchoolsCountry": "awardingCountryDisplayName",
      "educationSchoolsCity": "educationCity",
      "educationSchoolsDetails": "educationRemark"
    },
    eqheDetails: {
      "eqheDate": "dateOfEQHE",
      "eqheCity": "cityOfEQHE",
      "eqhecountry": "countryOfEQHEDisplayName",
      "eqheTitle": "originalTitleOfEQHE"
    }
  },

  picklistMappings: {
    programType: {
      "Undergraduate": "a0S0O00000UAl6iUAD",
      "Preparatory Programme": "a0S0O00000W5A7dUAF",
      "Postgraduate": "a0S0O00000UAl6nUAD",
      "Undergraduate + Foundation": "a0S6700001N7yM8EAJ"
    },
    language: {
      "English": "English",
      "German": "German",
      "Spanish": "Spanish",
      "French": "French"
    },
    title: {
      "mr": "Mr",
      "mrs": "Mrs",
      "ms": "Ms",
      "miss": "Miss",
    },
    titleDisplayName: {
      "mr": "Mr",
      "mrs": "Mrs",
      "ms": "Ms",
      "miss": "Miss",
    },
    correspondenceLanguage: {
      "german": "Deutsch - German",
      "english": "Englisch - English"
    },
    correspondenceLanguageDisplayName:{
      "german": "Deutsch - German",
      "english": "Englisch - English"
    },
    programDisplayName: {
      "a010X000012tJ3vQAE": "BA Communication Design",
      "a010X000012tM1DQAU": "BA Film and Motion Design",
      "a010X000012tM1EQAU": "BA Illustration",
      "a010X000012tM1GQAU": "BA Sports Science (Fitness and Health)",
      "a010X000012tM1HQAU": "BA Sports Science (Training and Performance)",
      "a010X000012tM1IQAU": "BSc Business and Management Studies",
      "a010X000012tM1JQAU": "BSc Business Psychology",
      "a010X000012tM1KQAU": "BSc Digital Media and Marketing",
      "a010X000012tM1LQAU": "BSc Digital Business and Data Science",
      "a010X000012tM1MQAU": "BSc Sport and Event Management",
      "a010X000012tM1TQAU": "Studienkolleg (Foundation Year - Business)",
      "a010X000012tM1UQAU": "Studienkolleg (Foundation Year - Technology)",
      "a010X000012tM1WQAU": "MA International Sport and Event Management",
      "a010X000012tM1XQAU": "MA Marketing Management",
      "a010X000012tM1YQAU": "MA New Media Design",
      "a010X000012tM1ZQAU": "MA Visual and Experience Design",
      "a010X000012tM1bQAE": "MSc Corporate Management",
      "a010X000012tM1dQAE": "Pre-master Business",
      "a010X000012tM1xQAE": "BA Game Design",
      "a010X000012tMj6QAE": "BA Photography and New Media",
      "a010X000012tMksQAE": "BSc Psychology",
      "a010X000012tMmZQAU": "MA Photography",
      "a010X000012tMmaQAE": "MBA Master of Business Administration",
      "a010X000013oMuSQAU": "MA Innovation Design Management",
      "a0167000017zeJRAAY": "BA Digital Product Management",
      "a0167000017zeJSAAY": "BSc Software Engineering",
      "a0167000017zeJTAAY": "MSc Data Science",
      "a0167000017zeJlAAI": "BA UI/UX Design",
      "a0167000017zeJmAAI": "MSc Software Engineering",
      "a0167000017zyCxAAI": "BA Communication Design (HTK Top-up)",
      "a01670000180m2uAAA": "MSc Psychology Rehabilitation",
      "a01670000180m2vAAA": "MSc Psychology Coaching",
      "a016700001CjFC3AAN": "BSc Sport and Event Management (Dual)",
      "a016700001CjFC8AAN": "BSc Digital Media and Marketing (Dual)",
      "a016700001CjFCSAA3": "BSc Software Engineering (Dual)",
      "a016700001CjFLPAA3": "BSc Business and Taxes (Dual)",
      "a016700001CjUtQAAV": "MA Communication Design",
      "a016700001CjnBnAAJ": "MBA Diplomacy",
      "a016700001CjnBsAAJ": "MBA Digital Technology",
      "a016700001CjnBxAAJ": "MBA Sustainable Water Management",
      "a016700001CjnC2AAJ": "MBA Shipping and Logistics",
      "a016700001CjnCbAAJ": "MSc International Business Management",
      "a016700001CjwgzAAB": "BA Hotel Management (dual)",
      "a016700001CkOryAAF": "B.Sc.Digital Business and Data Science with Foundation Diploma",
      "a016700001CkOs3AAF": "B.Sc. Digital Media and Marketing with Foundation Diploma",
      "a016700001CkOs9AAF": "B.Sc. Sport and Event Management with Foundation Diploma",
      "a016700001CkOsAAAV": "B.A. Film + Motion Design with Foundation Diploma",
      "a016700001CkOsDAAV": "B.Sc. Software Engineering with Foundation Diploma",
      "a016700001CkOsIAAV": "B.Sc. Business and Management Studies with Foundation Diploma",
      "a016700001CkOsJAAV": "B.A. Game Design with Foundation Diploma",
      "a016700001CkOsKAAV": "B.A. Illustration with Foundation Diploma",
      "a016700001CkOsNAAV": "B.A. Communication Design with Foundation Diploma",
      "a016700001CkOsSAAV": "B.A. Photography with Foundation Diploma",
      "a016700001CkOsXAAV": "B.A. UX/UI Design with Foundation Diploma",
      "a016700001HucXNAAZ": "MSc International Public Health Management",
      "a016700001HucXSAAZ": "MSc Asset Management",
      "a016700001HucXcAAJ": "MSc International Logistics and Transportation Management",
      "a016700001JXG8eAAH": "B.Sc. Psychology with Foundation Diploma",
      "a016700001KTxmbAAD": "MBA Financial Management",
      "a016700001KTxmlAAD": "MBA Investment Banking",
      "a016700001KTxmmAAD": "MBA Management Consulting",
      "a016700001KTxmnAAD": "MBA Health Service Management",
      "a016700001KTxmqAAD": "MBA Marketing Management",
      "a016700001KTxmrAAD": "MBA Sales Management",
      "a016700001KTxmvAAD": "MBA Project Management",
      "a016700001KTxn0AAD": "MBA Financial Risk Management",
      "a016700001KTxnAAAT": "MBA International Business",
      "a016700001KTxnFAAT": "MBA Health and Safety Leadership",
      "a016700001KTxnGAAT": "MBA Sports Management",
      "a016700001KTxnKAAT": "MBA Digital Health Transformation",
      "a016700001KTxnPAAT": "MSc Prevention and Therapy Management",
      "a016700001KTxnUAAT": "MA Design Leadership",
      "a016700001KTxnZAAT": "MA Digital Content Creation",
      "a016700001KTxneAAD": "BA Visual Communication",
      "a016700001KTxnoAAD": "BA Design and Management Studies",
      "a016700001KTxnuAAD": "B.Sc. Business Psychology with Foundation Diploma",
      "a016700001KU3gyAAD": "B.A. Digital Product Management with Foundation Diploma",
      "a01Tf000003jsk8IAA": "MSc Digital Transformations",
      "a01Tf000003k9EiIAI": "BSc Health Management",
      "a01Tf0000067WsYIAU": "MA Generative Design and AI",
      "a01Tf000006s66hIAA": "MSc Project Management International Programme",
      "a01Tf000006snrWIAQ": "MSc Strategic Business Management International Programme",
      "a01Tf000006snzaIAA": "MSc Marketing International Programme",
      "a01Tf000008ur1vIAA": "MSc Psychology Coaching and Counseling",
      "a01Tf0000098mAOIAY": "BA Sports Science (Fitness and Health) with Foundation Diploma",
      "a01Tf0000098mX1IAI": "BA Visual Communication with Foundation Diploma",
      "a01Tf0000098qilIAA": "BSc Software Engineering (Dual) with Foundation Diploma",
      "a01Tf0000098qxAIAQ": "BA Hotel Management (Dual) with Foundation Diploma",
      "a01Tf0000098u4iIAA": "BA Design and Management Studies with Foundation Diploma",
      "a01Tf00000993MUIAY": "BA Sports Science (Training and Performance) with Foundation Diploma",
      "a01Tf000009BW4HIAW": "BSc Health Management with Foundation Diploma",
      "a01Tf00000EaVzoIAF": "BSc Sport and Event Management with Pre-Course",
      "a01Tf00000EalEtIAJ": "BSc Software Engineering with Pre-Course",
      "a01Tf00000EamajIAB": "Pre-Master Psychology",
      "a01Tf00000EaoMRIAZ": "BA Design and Management Studies with Pre-Course"
    },
    startTerm: {
      "01t6700000E4lmRAAR": "2025-09-01",
      "01tTf000004RLakIAG": "2026-03-01",
      "01tTf000004RO7BIAW": "2026-09-01",
      "01t6700000E4ln0AAB": "2025-09-01",
      "01tTf000004ROGrIAO": "2026-09-01",
      "01tTf000004ROITIA4": "2026-03-01",
      "01t6700000E4lnAAAR": "2025-09-01",
      "01tTf000004RNPeIAO": "2026-03-01",
      "01tTf000004RONJIA4": "2026-09-01",
      "01tTf000002S3eXIAS": "2025-09-01",
      "01tTf000004ROQXIA4": "2026-03-01",
      "01tTf000004ROS9IAO": "2026-09-01",
      "01tTf000002S3g9IAC": "2025-09-01",
      "01tTf000004ROTlIAO": "2026-03-01",
      "01tTf000004ROVNIA4": "2026-09-01",
      "01t6700000E4loDAAR": "2025-09-01",
      "01tTf000004RKmlIAG": "2026-03-01",
      "01tTf000004RMTaIAO": "2026-09-01",
      "01tTf000004RNB8IAO": "2026-09-01",
      "01tTf000004ROjtIAG": "2026-09-01",
      "01tTf000004ROlVIAW": "2026-03-01",
      "01tTf000004ROn7IAG": "2026-03-01",
      "01tTf000004ROojIAG": "2026-03-01",
      "01tTf000004ROqLIAW": "2026-09-01",
      "01tTf000004VICrIAO": "2025-09-01",
      "01tTf000004VICsIAO": "2025-09-01",
      "01tTf000004VIETIA4": "2025-09-01",
      "01t6700000E4lloAAB": "2025-09-01",
      "01t6700000E4loSAAR": "2025-09-01",
      "01tTf000004ROwnIAG": "2026-09-01",
      "01tTf000004ROyPIAW": "2026-03-01",
      "01tTf000004RP1dIAG": "2026-09-01",
      "01tTf000004RP4rIAG": "2026-03-01",
      "01t6700000E4lorAAB": "2025-09-01",
      "01t6700000E4lzkAAB": "2025-09-01",
      "01tTf000004RLUIIA4": "2026-03-01",
      "01tTf000004RPG9IAO": "2026-09-01",
      "01tTf000004RPHlIAO": "2026-09-01",
      "01tTf000004RPJNIA4": "2026-03-01",
      "01t6700000E4lohAAB": "2025-09-01",
      "01t6700000E4lomAAB": "2025-09-01",
      "01t6700000E4lzfAAB": "2025-09-01",
      "01tTf000004RNeAIAW": "2026-09-01",
      "01tTf000004RP6TIAW": "2026-03-01",
      "01tTf000004RP85IAG": "2026-09-01",
      "01tTf000004RP9hIAG": "2026-03-01",
      "01tTf000004RPBJIA4": "2026-09-01",
      "01tTf000004RPCvIAO": "2026-03-01",
      "01t6700000E4lpfAAB": "2025-09-01",
      "01t6700000E4lpkAAB": "2025-09-01",
      "01t6700000E4lppAAB": "2025-09-01",
      "01tTf000004RPjBIAW": "2026-09-01",
      "01tTf000004RPknIAG": "2026-03-01",
      "01tTf000004RPo1IAG": "2026-09-01",
      "01tTf000004RPpdIAG": "2026-03-01",
      "01tTf000004RPrFIAW": "2026-09-01",
      "01tTf000004RPsrIAG": "2026-03-01",
      "01tTf000006MF3OIAW": "2025-09-01",
      "01tTf000006MF3PIAW": "2026-03-01",
      "01tTf000006MFmYIAW": "2026-09-01",
      "01t6700000E4lwvAAB": "2025-09-01",
      "01t6700000E4lx0AAB": "2025-09-01",
      "01t6700000E4lx5AAB": "2025-09-01",
      "01tTf000004VJiPIAW": "2026-09-01",
      "01tTf000004VJk1IAG": "2026-09-01",
      "01tTf000004VJldIAG": "2026-09-01",
      "01tTf000004VJthIAG": "2026-03-01",
      "01tTf000004VJvJIAW": "2026-03-01",
      "01tTf000004VJwvIAG": "2026-03-01",
      "01t6700000E4lxFAAR": "2025-09-01",
      "01t6700000E4lxKAAR": "2025-09-01",
      "01tTf000004VJorIAG": "2026-09-01",
      "01tTf000004VJqTIAW": "2026-09-01",
      "01t6700000E4lqTAAR": "2025-09-01",
      "01t6700000E4lqYAAR": "2025-09-01",
      "01t6700000E4lqdAAB": "2025-09-01",
      "01tTf000004RPMcIAO": "2026-09-01",
      "01tTf000004RPmQIAW": "2026-09-01",
      "01tTf000004RQYnIAO": "2026-09-01",
      "01tTf000004RQaPIAW": "2026-03-01",
      "01tTf000004RQc1IAG": "2026-03-01",
      "01tTf000004RQddIAG": "2026-03-01",
      "01t6700000E4lqiAAB": "2025-09-01",
      "01t6700000E4lqnAAB": "2025-09-01",
      "01t6700000E4lqsAAB": "2025-09-01",
      "01tTf000004RM5PIAW": "2026-09-01",
      "01tTf000004RO2MIAW": "2026-09-01",
      "01tTf000004ROdSIAW": "2026-03-01",
      "01tTf000004RQfFIAW": "2026-09-01",
      "01tTf000004RQgrIAG": "2026-03-01",
      "01tTf000004RQiTIAW": "2026-03-01",
      "01t6700000E4lrHAAR": "2025-09-01",
      "01t6700000E4lrMAAR": "2025-09-01",
      "01t6700000E4lrRAAR": "2025-09-01",
      "01tTf000004RM70IAG": "2026-03-01",
      "01tTf000004RQk5IAG": "2026-09-01",
      "01tTf000004RQlhIAG": "2026-09-01",
      "01tTf000004RQnJIAW": "2026-09-01",
      "01tTf000004RQovIAG": "2026-03-01",
      "01tTf000004RQs9IAG": "2026-03-01",
      "01t6700000E4lrIAAR": "2025-09-01",
      "01t6700000E4lrvAAB": "2025-09-01",
      "01t6700000E4m0JAAR": "2025-09-01",
      "01t6700000E4m0OAAR": "2025-09-01",
      "01t6700000E4m0TAAR": "2025-09-01",
      "01tTf000004RKTOIA4": "2026-03-01",
      "01tTf000004RLcOIAW": "2026-09-01",
      "01tTf000004RMtPIAW": "2026-09-01",
      "01tTf000004RNZKIA4": "2026-03-01",
      "01tTf000004RNfnIAG": "2026-03-01",
      "01tTf000004RQybIAG": "2026-09-01",
      "01tTf000004RR0DIAW": "2026-09-01",
      "01tTf000004RR1pIAG": "2026-09-01",
      "01tTf000004RR3RIAW": "2026-03-01",
      "01tTf000004RR53IAG": "2026-03-01",
      "01t6700000E4ltrAAB": "2025-09-01",
      "01t6700000E4ltwAAB": "2025-09-01",
      "01t6700000E4lu1AAB": "2025-09-01",
      "01tTf000004RNuIIAW": "2026-09-01",
      "01tTf000004RQdfIAG": "2026-09-01",
      "01tTf000004RS4LIAW": "2026-09-01",
      "01tTf000004RS7ZIAW": "2026-03-01",
      "01tTf000004RS9BIAW": "2026-03-01",
      "01tTf000004RSAnIAO": "2026-03-01",
      "01t6700000E4lwgAAB": "2025-09-01",
      "01t6700000E4lwlAAB": "2025-09-01",
      "01t6700000E4lwqAAB": "2025-09-01",
      "01tTf000003fORHIA2": "2025-09-01",
      "01tTf000003fORIIA2": "2026-03-01",
      "01tTf000004RKI6IAO": "2026-09-01",
      "01tTf000004ROX0IAO": "2026-03-01",
      "01tTf000004RP6UIAW": "2026-03-01",
      "01tTf000004RTIAIA4": "2026-09-01",
      "01tTf000004RTLNIA4": "2026-09-01",
      "01tTf000004RTMzIAO": "2026-03-01",
      "01tTf000004RTObIAO": "2026-09-01",
      "01t6700000E4ln5AAB": "2025-09-01",
      "01tTf000004RNHaIAO": "2026-09-01",
      "01tTf000004ROK5IAO": "2026-03-01",
      "01t6700000E4lnFAAR": "2025-09-01",
      "01tTf000004RM5OIAW": "2026-09-01",
      "01tTf000004ROOvIAO": "2026-03-01",
      "01t6700000E4lpLAAR": "2025-09-01",
      "01t6700000E4lpQAAR": "2025-09-01",
      "01tTf000004RJKWIA4": "2026-09-01",
      "01tTf000004RK3aIAG": "2026-03-01",
      "01tTf000004RPUfIAO": "2026-09-01",
      "01tTf000004RPWHIA4": "2026-03-01",
      "01t6700000E4lrWAAR": "2025-09-01",
      "01t6700000E4lrbAAB": "2025-09-01",
      "01t6700000E4lrgAAB": "2025-09-01",
      "01tTf000004RI0AIAW": "2026-03-01",
      "01tTf000004RPRSIA4": "2026-03-01",
      "01tTf000004RQYoIAO": "2026-09-01",
      "01tTf000004RQtlIAG": "2026-09-01",
      "01tTf000004RQvNIAW": "2026-09-01",
      "01tTf000004RQwzIAG": "2026-03-01",
      "01t6700000E4lt3AAB": "2025-09-01",
      "01t6700000E4lt8AAB": "2025-09-01",
      "01t6700000E4ltDAAR": "2025-09-01",
      "01tTf000004RLu6IAG": "2026-07-01",
      "01tTf000004RReXIAW": "2027-01-01",
      "01tUC000008Cj1aYAC": "2026-01-01",
      "01t6700000E4lqJAAR": "2025-09-01",
      "01t6700000E4lqOAAR": "2025-09-01",
      "01t6700000E4lzMAAR": "2025-09-01",
      "01t6700000E4m09AAB": "2025-09-01",
      "01t6700000E4m0EAAR": "2025-09-01",
      "01tTf000004RJylIAG": "2026-03-01",
      "01tTf000004RKy5IAG": "2026-03-01",
      "01tTf000004RLpGIAW": "2026-03-01",
      "01tTf000004RMIIIA4": "2026-09-01",
      "01tTf000004RMocIAG": "2026-09-01",
      "01tTf000004RNeBIAW": "2026-09-01",
      "01tTf000004RQSLIA4": "2026-09-01",
      "01tTf000004RQTxIAO": "2026-03-01",
      "01tTf000004RQVZIA4": "2026-09-01",
      "01tTf000004RQXBIA4": "2026-03-01",
      "01t6700000E4lzVAAR": "2025-09-01",
      "01tTf000004ROC1IAO": "2026-09-01",
      "01tTf000004ROFFIA4": "2026-03-01",
      "01t6700000E4lzpAAB": "2025-09-01",
      "01tTf000004RPb7IAG": "2026-09-01",
      "01tTf000004RPcjIAG": "2026-03-01",
      "01tTf000006MFuhIAG": "2025-09-01",
      "01tTf000006MFuiIAG": "2026-03-01",
      "01tTf000006MJLVIA4": "2026-09-01",
      "01t6700000E4luGAAR": "2025-09-01",
      "01t6700000E4luLAAR": "2025-09-01",
      "01t6700000E4m0dAAB": "2025-09-01",
      "01t6700000E4m0iAAB": "2025-09-01",
      "01t6700000E4m0nAAB": "2025-09-01",
      "01tTf000004RK8QIAW": "2026-09-01",
      "01tTf000004RKN3IAO": "2026-09-01",
      "01tTf000004RMgVIAW": "2026-09-01",
      "01tTf000004RPJOIA4": "2026-03-01",
      "01tTf000004RRWUIA4": "2026-03-01",
      "01tTf000004RRxuIAG": "2026-03-01",
      "01tTf000004RSCPIA4": "2026-09-01",
      "01tTf000004RSE1IAO": "2026-03-01",
      "01tTf000004RSFdIAO": "2026-03-01",
      "01tTf000004RSHFIA4": "2026-09-01",
      "01t6700000E4lzaAAB": "2025-09-01",
      "01tTf000004ROGsIAO": "2026-09-01",
      "01tTf000004ROWzIAO": "2026-03-01",
      "01t6700000E4lqFAAR": "2025-09-01",
      "01t6700000E4lwMAAR": "2025-09-01",
      "01t6700000E4lwRAAR": "2025-09-01",
      "01t6700000E4m0sAAB": "2025-09-01",
      "01t6700000E4m0xAAB": "2025-09-01",
      "01tTf000004RLFmIAO": "2026-09-01",
      "01tTf000004RLalIAG": "2026-03-01",
      "01tTf000004RNhOIAW": "2026-09-01",
      "01tTf000004RPo3IAG": "2026-09-01",
      "01tTf000004RQqZIAW": "2026-03-01",
      "01tTf000004RTA5IAO": "2026-09-01",
      "01tTf000004RTBhIAO": "2026-09-01",
      "01tTf000004RTDJIA4": "2026-03-01",
      "01tTf000004RTEvIAO": "2026-03-01",
      "01tTf000004RTGXIA4": "2026-03-01",
      "01t6700000B8lZ8AAJ": "2026-03-01",
      "01t6700000E5uVBAAZ": "2026-03-01",
      "01t6700000E5uVGAAZ": "2026-03-01",
      "01t6700000E5uVLAAZ": "2026-03-01",
      "01t6700000E5uVQAAZ": "2026-03-01",
      "01t6700000E4lqZAAR": "2025-09-01",
      "01t6700000E4lw2AAB": "2025-09-01",
      "01tTf000004RRjOIAW": "2026-09-01",
      "01tTf000004RT5FIAW": "2026-03-01",
      "01tTf000004RT6rIAG": "2026-09-01",
      "01tTf000004RT8TIAW": "2026-03-01",
      "01t6700000E4lvsAAB": "2025-09-01",
      "01t6700000E4lvxAAB": "2025-09-01",
      "01tTf000004RNJCIA4": "2026-03-01",
      "01tTf000004RPXuIAO": "2026-09-01",
      "01tTf000004RT21IAG": "2026-09-01",
      "01tTf000004RT3dIAG": "2026-03-01",
      "01t6700000E4lpuAAB": "2025-09-01",
      "01t6700000E4lpzAAB": "2025-09-01",
      "01tTf000004RJ7dIAG": "2026-09-01",
      "01tTf000004RPuTIAW": "2026-09-01",
      "01tTf000004RPw5IAG": "2026-03-01",
      "01tTf000004h4UIIAY": "2026-03-01",
      "01t6700000E4loOAAR": "2025-09-01",
      "01t6700000E4lpBAAR": "2025-09-01",
      "01tTf000004RNFyIAO": "2026-03-01",
      "01tTf000004RPKzIAO": "2026-09-01",
      "01tTf000004RPMbIAO": "2026-03-01",
      "01tTf000004RPODIA4": "2026-09-01",
      "01t6700000DCCwrAAH": "2025-09-01",
      "01tTf000004RPfxIAG": "2026-09-01",
      "01tTf000005770IIAQ": "2026-03-01",
      "01t6700000E4loIAAR": "2025-09-01",
      "01t6700000E4loNAAR": "2025-09-01",
      "01tTf000004ROtZIAW": "2026-09-01",
      "01tTf000004ROvBIAW": "2026-09-01",
      "01t6700000E4lmhAAB": "2025-09-01",
      "01t6700000E4lqEAAR": "2025-09-01",
      "01tTf000004RKtCIAW": "2026-09-01",
      "01tTf000004RL1GIAW": "2026-03-01",
      "01tTf000004RLkQIAW": "2026-03-01",
      "01tTf000004RQ0wIAG": "2026-09-01",
      "01tTf000004RQ2XIAW": "2026-03-01",
      "01tTf000004RQ49IAG": "2026-09-01",
      "01tTf000004VIEUIA4": "2025-09-01",
      "01t6700000E4lsFAAR": "2025-09-01",
      "01tTf000002H7iHIAS": "2025-09-01",
      "01tTf000004RN7uIAG": "2026-09-01",
      "01tTf000004RNmFIAW": "2026-03-01",
      "01tTf000004RPCwIAO": "2026-03-01",
      "01tTf000004RQdeIAG": "2026-09-01",
      "01t6700000E4m0YAAR": "2025-09-01",
      "01tTf000004RKYEIA4": "2026-09-01",
      "01tTf000004RNmEIAW": "2026-03-01",
      "01t6700000E4ltcAAB": "2025-09-01",
      "01tTf000004RRLCIA4": "2026-03-01",
      "01tTf000004RRxtIAG": "2026-09-01",
      "01t6700000E4loxAAB": "2025-09-01",
      "01tTf000002H7OvIAK": "2025-09-01",
      "01tTf000004ROjuIAG": "2026-09-01",
      "01tTf000004RRppIAG": "2026-09-01",
      "01tTf000004RRrRIAW": "2026-03-01",
      "01tTf000004RRt3IAG": "2026-03-01",
      "01t6700000E4lnCAAR": "2025-09-01",
      "01t6700000E4lq5AAB": "2025-09-01",
      "01t6700000E4lupAAB": "2025-09-01",
      "01t6700000E4luuAAB": "2025-09-01",
      "01t6700000E4luzAAB": "2025-09-01",
      "01t6700000E4lv4AAB": "2025-09-01",
      "01tTf000004RM5QIAW": "2026-03-01",
      "01tTf000004RPo2IAG": "2026-09-01",
      "01tTf000004RSVmIAO": "2026-03-01",
      "01tTf000004RSYzIAO": "2026-09-01",
      "01tTf000004RSabIAG": "2026-09-01",
      "01tTf000004RScDIAW": "2026-09-01",
      "01tTf000004RSdpIAG": "2026-03-01",
      "01tTf000004RSfRIAW": "2026-09-01",
      "01tTf000004RSh3IAG": "2026-09-01",
      "01tTf000004RSifIAG": "2026-03-01",
      "01tTf000004RSkHIAW": "2026-03-01",
      "01tTf000004RSltIAG": "2026-03-01",
      "01tTf000006MIqvIAG": "2025-09-01",
      "01tTf000006MJIIIA4": "2025-09-01",
      "01tTf000006MJIJIA4": "2026-03-01",
      "01tTf000006MJN7IAO": "2026-03-01",
      "01tTf000006MJN8IAO": "2026-09-01",
      "01tTf000006MJOjIAO": "2026-09-01",
      "01tTf0000028cRxIAI": "2025-09-01",
      "01tTf000004ROLhIAO": "2026-09-01",
      "01tTf00000575poIAA": "2026-03-01",
      "01t6700000E4lmMAAR": "2025-09-01",
      "01t6700000E4lzQAAR": "2025-09-01",
      "01tTf000004RNCkIAO": "2026-09-01",
      "01tTf000004RO2LIAW": "2026-09-01",
      "01tTf000004RO3xIAG": "2026-03-01",
      "01tTf000004RO5ZIAW": "2026-03-01",
      "01t6700000E4lm2AAB": "2025-09-01",
      "01t6700000E4lzGAAR": "2025-09-01",
      "01tTf000004RNnpIAG": "2026-09-01",
      "01tTf000004RNpRIAW": "2026-03-01",
      "01tTf000004RNr3IAG": "2026-09-01",
      "01tTf000004RNsfIAG": "2026-03-01",
      "01t6700000E4lmCAAR": "2025-09-01",
      "01tTf000004RNz7IAG": "2026-09-01",
      "01tTf000004RO0jIAG": "2026-03-01",
      "01t6700000E4llTAAR": "2025-09-01",
      "01tTf000004RNKnIAO": "2026-09-01",
      "01tTf000004RNMPIA4": "2026-03-01",
      "01t6700000E4lzLAAR": "2025-09-01",
      "01tTf000004RLdzIAG": "2026-03-01",
      "01tTf000004RNxVIAW": "2026-09-01",
      "01t6700000E4llnAAB": "2025-09-01",
      "01t6700000E4llsAAB": "2025-09-01",
      "01tTf000004RKmkIAG": "2026-09-01",
      "01tTf000004RNe9IAG": "2026-03-01",
      "01tTf000004RNflIAG": "2026-09-01",
      "01tTf000004RNhNIAW": "2026-03-01",
      "01t6700000E4llYAAR": "2025-09-01",
      "01tTf000004RNPdIAO": "2026-09-01",
      "01tTf000004RNRFIA4": "2026-03-01",
      "01t6700000E4lldAAB": "2025-09-01",
      "01tTf000004RNSrIAO": "2026-09-01",
      "01tTf000004RNW5IAO": "2026-03-01",
      "01t6700000E4llOAAR": "2025-09-01",
      "01tTf000004RNELIA4": "2026-09-01",
      "01tTf000004RNFxIAO": "2026-03-01",
      "01t6700000E4lliAAB": "2025-09-01",
      "01tTf000004RNXhIAO": "2026-09-01",
      "01tTf000004RNavIAG": "2026-03-01",
      "01t6700000E4lzBAAR": "2025-09-01",
      "01tTf000004RLEAIA4": "2026-03-01",
      "01tTf000004RNcXIAW": "2026-09-01",
      "01t6700000E4lvJAAR": "2025-09-01",
      "01t6700000E4lvOAAR": "2025-09-01",
      "01t6700000E4lvTAAR": "2025-09-01",
      "01tTf000004RLxKIAW": "2026-09-01",
      "01tTf000004RPMeIAO": "2026-09-01",
      "01tTf000004RSqjIAG": "2026-03-01",
      "01tTf000004RSsLIAW": "2026-03-01",
      "01tTf000004RStxIAG": "2026-03-01",
      "01tTf000004RSvZIAW": "2026-09-01",
      "01t6700000E4ltTAAR": "2025-09-01",
      "01t6700000E4lthAAB": "2025-09-01",
      "01t6700000E4ltmAAB": "2025-09-01",
      "01tTf000004RLu7IAG": "2026-09-01",
      "01tTf000004RMbeIAG": "2026-03-01",
      "01tTf000004RQ5mIAG": "2026-09-01",
      "01tTf000004RQXCIA4": "2026-03-01",
      "01tTf000004RRzVIAW": "2026-03-01",
      "01tTf000004RS17IAG": "2026-09-01",
      "01t6700000E4lnDAAR": "2025-09-01",
      "01t6700000E4lv9AAB": "2025-09-01",
      "01t6700000E4lvEAAR": "2025-09-01",
      "01tTf000004RNsgIAG": "2026-09-01",
      "01tTf000004ROGtIAO": "2026-03-01",
      "01tTf000004RPRTIA4": "2026-09-01",
      "01tTf000004RRBXIA4": "2026-03-01",
      "01tTf000004RSnVIAW": "2026-03-01",
      "01tTf000004RSp7IAG": "2026-09-01",
      "01t6700000E4lm7AAB": "2025-09-01",
      "01tTf000004RNuHIAW": "2026-09-01",
      "01tTf000004RNvtIAG": "2026-03-01",
      "01t6700000E4lrSAAR": "2025-09-01",
      "01t6700000E4lsKAAR": "2025-09-01",
      "01tTf000004RModIAG": "2026-09-01",
      "01tTf000004RR8HIAW": "2026-03-01",
      "01tTf000004RR9tIAG": "2026-03-01",
      "01tTf000004RRBVIA4": "2026-09-01",
      "01t6700000E4lkgAAB": "2025-09-01",
      "01t6700000E4lsjAAB": "2025-09-01",
      "01tTf000004RL66IAG": "2026-09-01",
      "01tTf000004ROdTIAW": "2026-03-01",
      "01tTf000004RRRdIAO": "2026-03-01",
      "01tTf000004RRTFIA4": "2026-09-01",
      "01t6700000E4lsoAAB": "2025-09-01",
      "01t6700000E4lstAAB": "2025-09-01",
      "01tTf000004RLR4IAO": "2026-09-01",
      "01tTf000004RMwcIAG": "2026-03-01",
      "01tTf000004RRUrIAO": "2026-03-01",
      "01tTf000004RRWTIA4": "2026-09-01",
      "01t6700000E4lsBAAR": "2025-09-01",
      "01tTf000004RLnfIAG": "2026-03-01",
      "01tTf000004RRLBIA4": "2026-09-01",
      "01t6700000E4lrwAAB": "2025-09-01",
      "01t6700000E4lsyAAB": "2025-09-01",
      "01tTf000004RMgUIAW": "2026-03-01",
      "01tTf000004RRY5IAO": "2026-09-01",
      "01tTf000004RRZhIAO": "2026-03-01",
      "01tTf000004RRbJIAW": "2026-09-01",
      "01t6700000E4lrxAAB": "2025-09-01",
      "01t6700000E4ltSAAR": "2025-09-01",
      "01tTf000004ROYcIAO": "2026-03-01",
      "01tTf000004RReYIAW": "2026-09-01",
      "01tTf000004RRkzIAG": "2026-03-01",
      "01tTf000004RRmbIAG": "2026-09-01",
      "01t6700000E4ltIAAR": "2025-09-01",
      "01t6700000E4ltNAAR": "2025-09-01",
      "01tTf000004RQqYIAW": "2026-03-01",
      "01tTf000004RRhlIAG": "2026-09-01",
      "01t6700000E4lltAAB": "2025-09-01",
      "01t6700000E4lsPAAR": "2025-09-01",
      "01tTf000004RLhCIAW": "2026-09-01",
      "01tTf000004RMRzIAO": "2026-03-01",
      "01tTf000004RRGLIA4": "2026-09-01",
      "01tTf000004RRHxIAO": "2026-03-01",
      "01t6700000E4lsZAAR": "2025-09-01",
      "01t6700000E4lseAAB": "2025-09-01",
      "01tTf000004RLhDIAW": "2026-03-01",
      "01tTf000004RPMdIAO": "2026-09-01",
      "01tTf000004RRMnIAO": "2026-03-01",
      "01tTf000004RROPIA4": "2026-09-01",
      "01t6700000E4lsUAAR": "2025-09-01",
      "01tTf000004ROVOIA4": "2026-03-01",
      "01tTf000004RPL0IAO": "2026-09-01",
      "01t6700000E4ltXAAR": "2025-09-01",
      "01tTf000004RKTPIA4": "2026-03-01",
      "01tTf000004RRwHIAW": "2026-09-01",
      "01t6700000E4lsAAAR": "2025-09-01",
      "01tTf000004RKy6IAG": "2026-09-01",
      "01tTf000004RR6fIAG": "2026-03-01",
      "01t6700000E4lvYAAR": "2025-09-01",
      "01t6700000E4lvdAAB": "2025-09-01",
      "01t6700000E4lviAAB": "2025-09-01",
      "01tTf000004RIeUIAW": "2026-03-01",
      "01tTf000004RKRnIAO": "2026-03-01",
      "01tTf000004RLXXIA4": "2026-03-01",
      "01tTf000004RMGgIAO": "2026-09-01",
      "01tTf000004RSxBIAW": "2026-09-01",
      "01tTf000004RSynIAG": "2026-09-01",
      "01tTf00000270YRIAY": "2025-09-01",
      "01tTf0000028bUIIAY": "2025-09-01",
      "01tTf0000028cWnIAI": "2025-09-01",
      "01tTf000004RO8oIAG": "2026-09-01",
      "01tTf000004RQ5lIAG": "2026-03-01",
      "01tTf000004RQ7NIAW": "2026-03-01",
      "01tTf000004RQ8zIAG": "2026-03-01",
      "01tTf000004RQAbIAO": "2026-09-01",
      "01tTf000004RQCDIA4": "2026-09-01",
      "01tTf0000028cYPIAY": "2025-09-01",
      "01tTf0000028ca1IAA": "2025-09-01",
      "01tTf0000028cbdIAA": "2025-09-01",
      "01tTf000004RJCTIA4": "2026-09-01",
      "01tTf000004RNfmIAG": "2026-03-01",
      "01tTf000004RQDpIAO": "2026-03-01",
      "01tTf000004RQFRIA4": "2026-03-01",
      "01tTf000004RQH3IAO": "2026-09-01",
      "01tTf000004RQIfIAO": "2026-09-01",
      "01t6700000E4lntAAB": "2025-09-01",
      "01t6700000E4lnyAAB": "2025-09-01",
      "01tTf000004ROYbIAO": "2026-03-01",
      "01tTf000004RObpIAG": "2026-03-01",
      "01tTf000004ROf3IAG": "2026-09-01",
      "01tTf000004ROgfIAG": "2026-09-01",
      "01t6700000E4lmWAAR": "2025-09-01",
      "01tTf000004RO8nIAG": "2026-03-01",
      "01tTf000004ROAPIA4": "2026-09-01",
      "01tTf000006M50oIAC": "2026-03-01",
      "01tTf000006M50pIAC": "2026-09-01",
      "01tTf000006MFmZIAW": "2025-09-01",
      "01t6700000E4llxAAB": "2025-09-01",
      "01tTf000004RNkbIAG": "2026-03-01",
      "01tTf000004RNmDIAW": "2026-09-01",
      "01t6700000E4ougAAB": "2025-09-01",
      "01tTf000004RNHZIA4": "2026-03-01",
      "01tTf000004RNJBIA4": "2026-09-01",
      "01tTf000002Hk71IAC": "2025-09-01",
      "01tTf000002Hk72IAC": "2025-09-01",
      "01tTf000002Hk73IAC": "2025-09-01",
      "01tTf000004ROOwIAO": "2026-03-01",
      "01tTf000004RSIrIAO": "2026-03-01",
      "01tTf000004RSKTIA4": "2026-03-01",
      "01tTf000004RSNhIAO": "2026-09-01",
      "01tTf000004RSPJIA4": "2026-09-01",
      "01tTf000004RSQvIAO": "2026-09-01",
      "01tTf000002H6PxIAK": "2025-09-01",
      "01tTf000004RJykIAG": "2026-03-01",
      "01tTf000004RPT3IAO": "2026-09-01",
      "01tTf000003BRklIAG": "2025-09-01",
      "01tTf000003BRkmIAG": "2025-09-01",
      "01tTf000003BRknIAG": "2025-09-01",
      "01tTf000004RNB9IAO": "2026-09-01",
      "01tTf000004RQKHIA4": "2026-03-01",
      "01tTf000004RQLtIAO": "2026-03-01",
      "01tTf000004RQNVIA4": "2026-03-01",
      "01tTf000004RQP7IAO": "2026-09-01",
      "01tTf000004RQQjIAO": "2026-09-01",
      "01tTf000003SzzuIAC": "2025-09-01",
      "01tTf000003SzzvIAC": "2026-01-01",
      "01tTf000004RTI9IAO": "2026-06-01",
      "01tTf000003SzzoIAC": "2025-09-01",
      "01tTf000003SzzpIAC": "2026-01-01",
      "01tTf000004RTJlIAO": "2026-06-01",
      "01tTf000003T000IAC": "2025-09-01",
      "01tTf000003T001IAC": "2026-01-01",
      "01tTf000004LJ3lIAG": "2025-09-01",
      "01tTf000004LJ8bIAG": "2026-03-01",
      "01tTf000004iBa9IAE": "2026-09-01",
      "01tTf000004QRiEIAW": "2026-03-01",
      "01tTf000004QS9dIAG": "2025-09-01",
      "01tTf000004iBVJIA2": "2026-09-01",
      "01tTf000004QS4nIAG": "2025-09-01",
      "01tTf000004QS6PIAW": "2026-03-01",
      "01tTf000004iBYXIA2": "2026-09-01",
      "01tTf000004VNcLIAW": "2025-09-01",
      "01tTf000004iBofIAE": "2026-09-01",
      "01tTf000005770KIAQ": "2026-03-01",
      "01tTf000004QSJJIA4": "2025-09-01",
      "01tTf000004i7OiIAI": "2026-09-01",
      "01tTf00000575pqIAA": "2026-03-01",
      "01tTf000004QRyLIAW": "2025-09-01",
      "01tTf000004QRzxIAG": "2026-03-01",
      "01tTf000004iBThIAM": "2026-09-01",
      "01tTf000004QSETIA4": "2025-09-01",
      "01tTf000004QSG5IAO": "2026-03-01",
      "01tTf000004iBWvIAM": "2026-09-01",
      "01tTf000004RJNeIAO": "2026-03-01",
      "01tTf000004RM5NIAW": "2025-09-01",
      "01tTf000004RM6zIAG": "2026-09-01",
      "01tTf000006Q3rdIAC": "2025-09-01",
      "01tTf000006Q3reIAC": "2026-03-01",
      "01tTf000006Q3tFIAS": "2026-09-01",
      "01tTf000006Q3tGIAS": "2025-09-01",
      "01tTf000006Q3urIAC": "2026-03-01",
      "01tTf000006Q3usIAC": "2026-09-01",
      "01tTf000006Q3N0IAK": "2026-03-01",
      "01tTf000006Q3N1IAK": "2026-09-01",
      "01tTf000006Q3q2IAC": "2025-09-01",
      "01tTf000006Q2qkIAC": "2026-09-01",
      "01tTf000006Q3wTIAS": "2025-09-01",
      "01tTf000006Q3wUIAS": "2026-03-01"
    },
    startTermDisplayName: {
      "01t6700000E4lmRAAR": "September 2025",
      "01tTf000004RLakIAG": "March 2026",
      "01tTf000004RO7BIAW": "September 2026",
      "01t6700000E4ln0AAB": "September 2025",
      "01tTf000004ROGrIAO": "September 2026",
      "01tTf000004ROITIA4": "March 2026",
      "01t6700000E4lnAAAR": "September 2025",
      "01tTf000004RNPeIAO": "March 2026",
      "01tTf000004RONJIA4": "September 2026",
      "01tTf000002S3eXIAS": "September 2025",
      "01tTf000004ROQXIA4": "March 2026",
      "01tTf000004ROS9IAO": "September 2026",
      "01tTf000002S3g9IAC": "September 2025",
      "01tTf000004ROTlIAO": "March 2026",
      "01tTf000004ROVNIA4": "September 2026",
      "01t6700000E4loDAAR": "September 2025",
      "01tTf000004RKmlIAG": "March 2026",
      "01tTf000004RMTaIAO": "September 2026",
      "01tTf000004RNB8IAO": "September 2026",
      "01tTf000004ROjtIAG": "September 2026",
      "01tTf000004ROlVIAW": "March 2026",
      "01tTf000004ROn7IAG": "March 2026",
      "01tTf000004ROojIAG": "March 2026",
      "01tTf000004ROqLIAW": "September 2026",
      "01tTf000004VICrIAO": "September 2025",
      "01tTf000004VICsIAO": "September 2025",
      "01tTf000004VIETIA4": "September 2025",
      "01t6700000E4lloAAB": "September 2025",
      "01t6700000E4loSAAR": "September 2025",
      "01tTf000004ROwnIAG": "September 2026",
      "01tTf000004ROyPIAW": "March 2026",
      "01tTf000004RP1dIAG": "September 2026",
      "01tTf000004RP4rIAG": "March 2026",
      "01t6700000E4lorAAB": "September 2025",
      "01t6700000E4lzkAAB": "September 2025",
      "01tTf000004RLUIIA4": "March 2026",
      "01tTf000004RPG9IAO": "September 2026",
      "01tTf000004RPHlIAO": "September 2026",
      "01tTf000004RPJNIA4": "March 2026",
      "01t6700000E4lohAAB": "September 2025",
      "01t6700000E4lomAAB": "September 2025",
      "01t6700000E4lzfAAB": "September 2025",
      "01tTf000004RNeAIAW": "September 2026",
      "01tTf000004RP6TIAW": "March 2026",
      "01tTf000004RP85IAG": "September 2026",
      "01tTf000004RP9hIAG": "March 2026",
      "01tTf000004RPBJIA4": "September 2026",
      "01tTf000004RPCvIAO": "March 2026",
      "01t6700000E4lpfAAB": "September 2025",
      "01t6700000E4lpkAAB": "September 2025",
      "01t6700000E4lppAAB": "September 2025",
      "01tTf000004RPjBIAW": "September 2026",
      "01tTf000004RPknIAG": "March 2026",
      "01tTf000004RPo1IAG": "September 2026",
      "01tTf000004RPpdIAG": "March 2026",
      "01tTf000004RPrFIAW": "September 2026",
      "01tTf000004RPsrIAG": "March 2026",
      "01tTf000006MF3OIAW": "September 2025",
      "01tTf000006MF3PIAW": "March 2026",
      "01tTf000006MFmYIAW": "September 2026",
      "01t6700000E4lwvAAB": "September 2025",
      "01t6700000E4lx0AAB": "September 2025",
      "01t6700000E4lx5AAB": "September 2025",
      "01tTf000004VJiPIAW": "September 2026",
      "01tTf000004VJk1IAG": "September 2026",
      "01tTf000004VJldIAG": "September 2026",
      "01tTf000004VJthIAG": "March 2026",
      "01tTf000004VJvJIAW": "March 2026",
      "01tTf000004VJwvIAG": "March 2026",
      "01t6700000E4lxFAAR": "September 2025",
      "01t6700000E4lxKAAR": "September 2025",
      "01tTf000004VJorIAG": "September 2026",
      "01tTf000004VJqTIAW": "September 2026",
      "01t6700000E4lqTAAR": "September 2025",
      "01t6700000E4lqYAAR": "September 2025",
      "01t6700000E4lqdAAB": "September 2025",
      "01tTf000004RPMcIAO": "September 2026",
      "01tTf000004RPmQIAW": "September 2026",
      "01tTf000004RQYnIAO": "September 2026",
      "01tTf000004RQaPIAW": "March 2026",
      "01tTf000004RQc1IAG": "March 2026",
      "01tTf000004RQddIAG": "March 2026",
      "01t6700000E4lqiAAB": "September 2025",
      "01t6700000E4lqnAAB": "September 2025",
      "01t6700000E4lqsAAB": "September 2025",
      "01tTf000004RM5PIAW": "September 2026",
      "01tTf000004RO2MIAW": "September 2026",
      "01tTf000004ROdSIAW": "March 2026",
      "01tTf000004RQfFIAW": "September 2026",
      "01tTf000004RQgrIAG": "March 2026",
      "01tTf000004RQiTIAW": "March 2026",
      "01t6700000E4lrHAAR": "September 2025",
      "01t6700000E4lrMAAR": "September 2025",
      "01t6700000E4lrRAAR": "September 2025",
      "01tTf000004RM70IAG": "March 2026",
      "01tTf000004RQk5IAG": "September 2026",
      "01tTf000004RQlhIAG": "September 2026",
      "01tTf000004RQnJIAW": "September 2026",
      "01tTf000004RQovIAG": "March 2026",
      "01tTf000004RQs9IAG": "March 2026",
      "01t6700000E4lrIAAR": "September 2025",
      "01t6700000E4lrvAAB": "September 2025",
      "01t6700000E4m0JAAR": "September 2025",
      "01t6700000E4m0OAAR": "September 2025",
      "01t6700000E4m0TAAR": "September 2025",
      "01tTf000004RKTOIA4": "March 2026",
      "01tTf000004RLcOIAW": "September 2026",
      "01tTf000004RMtPIAW": "September 2026",
      "01tTf000004RNZKIA4": "March 2026",
      "01tTf000004RNfnIAG": "March 2026",
      "01tTf000004RQybIAG": "September 2026",
      "01tTf000004RR0DIAW": "September 2026",
      "01tTf000004RR1pIAG": "September 2026",
      "01tTf000004RR3RIAW": "March 2026",
      "01tTf000004RR53IAG": "March 2026",
      "01t6700000E4ltrAAB": "September 2025",
      "01t6700000E4ltwAAB": "September 2025",
      "01t6700000E4lu1AAB": "September 2025",
      "01tTf000004RNuIIAW": "September 2026",
      "01tTf000004RQdfIAG": "September 2026",
      "01tTf000004RS4LIAW": "September 2026",
      "01tTf000004RS7ZIAW": "March 2026",
      "01tTf000004RS9BIAW": "March 2026",
      "01tTf000004RSAnIAO": "March 2026",
      "01t6700000E4lwgAAB": "September 2025",
      "01t6700000E4lwlAAB": "September 2025",
      "01t6700000E4lwqAAB": "September 2025",
      "01tTf000003fORHIA2": "September 2025",
      "01tTf000003fORIIA2": "March 2026",
      "01tTf000004RKI6IAO": "September 2026",
      "01tTf000004ROX0IAO": "March 2026",
      "01tTf000004RP6UIAW": "March 2026",
      "01tTf000004RTIAIA4": "September 2026",
      "01tTf000004RTLNIA4": "September 2026",
      "01tTf000004RTMzIAO": "March 2026",
      "01tTf000004RTObIAO": "September 2026",
      "01t6700000E4ln5AAB": "September 2025",
      "01tTf000004RNHaIAO": "September 2026",
      "01tTf000004ROK5IAO": "March 2026",
      "01t6700000E4lnFAAR": "September 2025",
      "01tTf000004RM5OIAW": "September 2026",
      "01tTf000004ROOvIAO": "March 2026",
      "01t6700000E4lpLAAR": "September 2025",
      "01t6700000E4lpQAAR": "September 2025",
      "01tTf000004RJKWIA4": "September 2026",
      "01tTf000004RK3aIAG": "March 2026",
      "01tTf000004RPUfIAO": "September 2026",
      "01tTf000004RPWHIA4": "March 2026",
      "01t6700000E4lrWAAR": "September 2025",
      "01t6700000E4lrbAAB": "September 2025",
      "01t6700000E4lrgAAB": "September 2025",
      "01tTf000004RI0AIAW": "March 2026",
      "01tTf000004RPRSIA4": "March 2026",
      "01tTf000004RQYoIAO": "September 2026",
      "01tTf000004RQtlIAG": "September 2026",
      "01tTf000004RQvNIAW": "September 2026",
      "01tTf000004RQwzIAG": "March 2026",
      "01t6700000E4lt3AAB": "September 2025",
      "01t6700000E4lt8AAB": "September 2025",
      "01t6700000E4ltDAAR": "September 2025",
      "01tTf000004RLu6IAG": "July 2026",
      "01tTf000004RReXIAW": "January 2027",
      "01tUC000008Cj1aYAC": "January 2026",
      "01t6700000E4lqJAAR": "September 2025",
      "01t6700000E4lqOAAR": "September 2025",
      "01t6700000E4lzMAAR": "September 2025",
      "01t6700000E4m09AAB": "September 2025",
      "01t6700000E4m0EAAR": "September 2025",
      "01tTf000004RJylIAG": "March 2026",
      "01tTf000004RKy5IAG": "March 2026",
      "01tTf000004RLpGIAW": "March 2026",
      "01tTf000004RMIIIA4": "September 2026",
      "01tTf000004RMocIAG": "September 2026",
      "01tTf000004RNeBIAW": "September 2026",
      "01tTf000004RQSLIA4": "September 2026",
      "01tTf000004RQTxIAO": "March 2026",
      "01tTf000004RQVZIA4": "September 2026",
      "01tTf000004RQXBIA4": "March 2026",
      "01t6700000E4lzVAAR": "September 2025",
      "01tTf000004ROC1IAO": "September 2026",
      "01tTf000004ROFFIA4": "March 2026",
      "01t6700000E4lzpAAB": "September 2025",
      "01tTf000004RPb7IAG": "September 2026",
      "01tTf000004RPcjIAG": "March 2026",
      "01tTf000006MFuhIAG": "September 2025",
      "01tTf000006MFuiIAG": "March 2026",
      "01tTf000006MJLVIA4": "September 2026",
      "01t6700000E4luGAAR": "September 2025",
      "01t6700000E4luLAAR": "September 2025",
      "01t6700000E4m0dAAB": "September 2025",
      "01t6700000E4m0iAAB": "September 2025",
      "01t6700000E4m0nAAB": "September 2025",
      "01tTf000004RK8QIAW": "September 2026",
      "01tTf000004RKN3IAO": "September 2026",
      "01tTf000004RMgVIAW": "September 2026",
      "01tTf000004RPJOIA4": "March 2026",
      "01tTf000004RRWUIA4": "March 2026",
      "01tTf000004RRxuIAG": "March 2026",
      "01tTf000004RSCPIA4": "September 2026",
      "01tTf000004RSE1IAO": "March 2026",
      "01tTf000004RSFdIAO": "March 2026",
      "01tTf000004RSHFIA4": "September 2026",
      "01t6700000E4lzaAAB": "September 2025",
      "01tTf000004ROGsIAO": "September 2026",
      "01tTf000004ROWzIAO": "March 2026",
      "01t6700000E4lqFAAR": "September 2025",
      "01t6700000E4lwMAAR": "September 2025",
      "01t6700000E4lwRAAR": "September 2025",
      "01t6700000E4m0sAAB": "September 2025",
      "01t6700000E4m0xAAB": "September 2025",
      "01tTf000004RLFmIAO": "September 2026",
      "01tTf000004RLalIAG": "March 2026",
      "01tTf000004RNhOIAW": "September 2026",
      "01tTf000004RPo3IAG": "September 2026",
      "01tTf000004RQqZIAW": "March 2026",
      "01tTf000004RTA5IAO": "September 2026",
      "01tTf000004RTBhIAO": "September 2026",
      "01tTf000004RTDJIA4": "March 2026",
      "01tTf000004RTEvIAO": "March 2026",
      "01tTf000004RTGXIA4": "March 2026",
      "01t6700000B8lZ8AAJ": "March 2026",
      "01t6700000E5uVBAAZ": "March 2026",
      "01t6700000E5uVGAAZ": "March 2026",
      "01t6700000E5uVLAAZ": "March 2026",
      "01t6700000E5uVQAAZ": "March 2026",
      "01t6700000E4lqZAAR": "September 2025",
      "01t6700000E4lw2AAB": "September 2025",
      "01tTf000004RRjOIAW": "September 2026",
      "01tTf000004RT5FIAW": "March 2026",
      "01tTf000004RT6rIAG": "September 2026",
      "01tTf000004RT8TIAW": "March 2026",
      "01t6700000E4lvsAAB": "September 2025",
      "01t6700000E4lvxAAB": "September 2025",
      "01tTf000004RNJCIA4": "March 2026",
      "01tTf000004RPXuIAO": "September 2026",
      "01tTf000004RT21IAG": "September 2026",
      "01tTf000004RT3dIAG": "March 2026",
      "01t6700000E4lpuAAB": "September 2025",
      "01t6700000E4lpzAAB": "September 2025",
      "01tTf000004RJ7dIAG": "September 2026",
      "01tTf000004RPuTIAW": "September 2026",
      "01tTf000004RPw5IAG": "March 2026",
      "01tTf000004h4UIIAY": "March 2026",
      "01t6700000E4loOAAR": "September 2025",
      "01t6700000E4lpBAAR": "September 2025",
      "01tTf000004RNFyIAO": "March 2026",
      "01tTf000004RPKzIAO": "September 2026",
      "01tTf000004RPMbIAO": "March 2026",
      "01tTf000004RPODIA4": "September 2026",
      "01t6700000DCCwrAAH": "September 2025",
      "01tTf000004RPfxIAG": "September 2026",
      "01tTf000005770IIAQ": "March 2026",
      "01t6700000E4loIAAR": "September 2025",
      "01t6700000E4loNAAR": "September 2025",
      "01tTf000004ROtZIAW": "September 2026",
      "01tTf000004ROvBIAW": "September 2026",
      "01t6700000E4lmhAAB": "September 2025",
      "01t6700000E4lqEAAR": "September 2025",
      "01tTf000004RKtCIAW": "September 2026",
      "01tTf000004RL1GIAW": "March 2026",
      "01tTf000004RLkQIAW": "March 2026",
      "01tTf000004RQ0wIAG": "September 2026",
      "01tTf000004RQ2XIAW": "March 2026",
      "01tTf000004RQ49IAG": "September 2026",
      "01tTf000004VIEUIA4": "September 2025",
      "01t6700000E4lsFAAR": "September 2025",
      "01tTf000002H7iHIAS": "September 2025",
      "01tTf000004RN7uIAG": "September 2026",
      "01tTf000004RNmFIAW": "March 2026",
      "01tTf000004RPCwIAO": "March 2026",
      "01tTf000004RQdeIAG": "September 2026",
      "01t6700000E4m0YAAR": "September 2025",
      "01tTf000004RKYEIA4": "September 2026",
      "01tTf000004RNmEIAW": "March 2026",
      "01t6700000E4ltcAAB": "September 2025",
      "01tTf000004RRLCIA4": "March 2026",
      "01tTf000004RRxtIAG": "September 2026",
      "01t6700000E4loxAAB": "September 2025",
      "01tTf000002H7OvIAK": "September 2025",
      "01tTf000004ROjuIAG": "September 2026",
      "01tTf000004RRppIAG": "September 2026",
      "01tTf000004RRrRIAW": "March 2026",
      "01tTf000004RRt3IAG": "March 2026",
      "01t6700000E4lnCAAR": "September 2025",
      "01t6700000E4lq5AAB": "September 2025",
      "01t6700000E4lupAAB": "September 2025",
      "01t6700000E4luuAAB": "September 2025",
      "01t6700000E4luzAAB": "September 2025",
      "01t6700000E4lv4AAB": "September 2025",
      "01tTf000004RM5QIAW": "March 2026",
      "01tTf000004RPo2IAG": "September 2026",
      "01tTf000004RSVmIAO": "March 2026",
      "01tTf000004RSYzIAO": "September 2026",
      "01tTf000004RSabIAG": "September 2026",
      "01tTf000004RScDIAW": "September 2026",
      "01tTf000004RSdpIAG": "March 2026",
      "01tTf000004RSfRIAW": "September 2026",
      "01tTf000004RSh3IAG": "September 2026",
      "01tTf000004RSifIAG": "March 2026",
      "01tTf000004RSkHIAW": "March 2026",
      "01tTf000004RSltIAG": "March 2026",
      "01tTf000006MIqvIAG": "September 2025",
      "01tTf000006MJIIIA4": "September 2025",
      "01tTf000006MJIJIA4": "March 2026",
      "01tTf000006MJN7IAO": "March 2026",
      "01tTf000006MJN8IAO": "September 2026",
      "01tTf000006MJOjIAO": "September 2026",
      "01tTf0000028cRxIAI": "September 2025",
      "01tTf000004ROLhIAO": "September 2026",
      "01tTf00000575poIAA": "March 2026",
      "01t6700000E4lmMAAR": "September 2025",
      "01t6700000E4lzQAAR": "September 2025",
      "01tTf000004RNCkIAO": "September 2026",
      "01tTf000004RO2LIAW": "September 2026",
      "01tTf000004RO3xIAG": "March 2026",
      "01tTf000004RO5ZIAW": "March 2026",
      "01t6700000E4lm2AAB": "September 2025",
      "01t6700000E4lzGAAR": "September 2025",
      "01tTf000004RNnpIAG": "September 2026",
      "01tTf000004RNpRIAW": "March 2026",
      "01tTf000004RNr3IAG": "September 2026",
      "01tTf000004RNsfIAG": "March 2026",
      "01t6700000E4lmCAAR": "September 2025",
      "01tTf000004RNz7IAG": "September 2026",
      "01tTf000004RO0jIAG": "March 2026",
      "01t6700000E4llTAAR": "September 2025",
      "01tTf000004RNKnIAO": "September 2026",
      "01tTf000004RNMPIA4": "March 2026",
      "01t6700000E4lzLAAR": "September 2025",
      "01tTf000004RLdzIAG": "March 2026",
      "01tTf000004RNxVIAW": "September 2026",
      "01t6700000E4llnAAB": "September 2025",
      "01t6700000E4llsAAB": "September 2025",
      "01tTf000004RKmkIAG": "September 2026",
      "01tTf000004RNe9IAG": "March 2026",
      "01tTf000004RNflIAG": "September 2026",
      "01tTf000004RNhNIAW": "March 2026",
      "01t6700000E4llYAAR": "September 2025",
      "01tTf000004RNPdIAO": "September 2026",
      "01tTf000004RNRFIA4": "March 2026",
      "01t6700000E4lldAAB": "September 2025",
      "01tTf000004RNSrIAO": "September 2026",
      "01tTf000004RNW5IAO": "March 2026",
      "01t6700000E4llOAAR": "September 2025",
      "01tTf000004RNELIA4": "September 2026",
      "01tTf000004RNFxIAO": "March 2026",
      "01t6700000E4lliAAB": "September 2025",
      "01tTf000004RNXhIAO": "September 2026",
      "01tTf000004RNavIAG": "March 2026",
      "01t6700000E4lzBAAR": "September 2025",
      "01tTf000004RLEAIA4": "March 2026",
      "01tTf000004RNcXIAW": "September 2026",
      "01t6700000E4lvJAAR": "September 2025",
      "01t6700000E4lvOAAR": "September 2025",
      "01t6700000E4lvTAAR": "September 2025",
      "01tTf000004RLxKIAW": "September 2026",
      "01tTf000004RPMeIAO": "September 2026",
      "01tTf000004RSqjIAG": "March 2026",
      "01tTf000004RSsLIAW": "March 2026",
      "01tTf000004RStxIAG": "March 2026",
      "01tTf000004RSvZIAW": "September 2026",
      "01t6700000E4ltTAAR": "September 2025",
      "01t6700000E4lthAAB": "September 2025",
      "01t6700000E4ltmAAB": "September 2025",
      "01tTf000004RLu7IAG": "September 2026",
      "01tTf000004RMbeIAG": "March 2026",
      "01tTf000004RQ5mIAG": "September 2026",
      "01tTf000004RQXCIA4": "March 2026",
      "01tTf000004RRzVIAW": "March 2026",
      "01tTf000004RS17IAG": "September 2026",
      "01t6700000E4lnDAAR": "September 2025",
      "01t6700000E4lv9AAB": "September 2025",
      "01t6700000E4lvEAAR": "September 2025",
      "01tTf000004RNsgIAG": "September 2026",
      "01tTf000004ROGtIAO": "March 2026",
      "01tTf000004RPRTIA4": "September 2026",
      "01tTf000004RRBXIA4": "March 2026",
      "01tTf000004RSnVIAW": "March 2026",
      "01tTf000004RSp7IAG": "September 2026",
      "01t6700000E4lm7AAB": "September 2025",
      "01tTf000004RNuHIAW": "September 2026",
      "01tTf000004RNvtIAG": "March 2026",
      "01t6700000E4lrSAAR": "September 2025",
      "01t6700000E4lsKAAR": "September 2025",
      "01tTf000004RModIAG": "September 2026",
      "01tTf000004RR8HIAW": "March 2026",
      "01tTf000004RR9tIAG": "March 2026",
      "01tTf000004RRBVIA4": "September 2026",
      "01t6700000E4lkgAAB": "September 2025",
      "01t6700000E4lsjAAB": "September 2025",
      "01tTf000004RL66IAG": "September 2026",
      "01tTf000004ROdTIAW": "March 2026",
      "01tTf000004RRRdIAO": "March 2026",
      "01tTf000004RRTFIA4": "September 2026",
      "01t6700000E4lsoAAB": "September 2025",
      "01t6700000E4lstAAB": "September 2025",
      "01tTf000004RLR4IAO": "September 2026",
      "01tTf000004RMwcIAG": "March 2026",
      "01tTf000004RRUrIAO": "March 2026",
      "01tTf000004RRWTIA4": "September 2026",
      "01t6700000E4lsBAAR": "September 2025",
      "01tTf000004RLnfIAG": "March 2026",
      "01tTf000004RRLBIA4": "September 2026",
      "01t6700000E4lrwAAB": "September 2025",
      "01t6700000E4lsyAAB": "September 2025",
      "01tTf000004RMgUIAW": "March 2026",
      "01tTf000004RRY5IAO": "September 2026",
      "01tTf000004RRZhIAO": "March 2026",
      "01tTf000004RRbJIAW": "September 2026",
      "01t6700000E4lrxAAB": "September 2025",
      "01t6700000E4ltSAAR": "September 2025",
      "01tTf000004ROYcIAO": "March 2026",
      "01tTf000004RReYIAW": "September 2026",
      "01tTf000004RRkzIAG": "March 2026",
      "01tTf000004RRmbIAG": "September 2026",
      "01t6700000E4ltIAAR": "September 2025",
      "01t6700000E4ltNAAR": "September 2025",
      "01tTf000004RQqYIAW": "March 2026",
      "01tTf000004RRhlIAG": "September 2026",
      "01t6700000E4lltAAB": "September 2025",
      "01t6700000E4lsPAAR": "September 2025",
      "01tTf000004RLhCIAW": "September 2026",
      "01tTf000004RMRzIAO": "March 2026",
      "01tTf000004RRGLIA4": "September 2026",
      "01tTf000004RRHxIAO": "March 2026",
      "01t6700000E4lsZAAR": "September 2025",
      "01t6700000E4lseAAB": "September 2025",
      "01tTf000004RLhDIAW": "March 2026",
      "01tTf000004RPMdIAO": "September 2026",
      "01tTf000004RRMnIAO": "March 2026",
      "01tTf000004RROPIA4": "September 2026",
      "01t6700000E4lsUAAR": "September 2025",
      "01tTf000004ROVOIA4": "March 2026",
      "01tTf000004RPL0IAO": "September 2026",
      "01t6700000E4ltXAAR": "September 2025",
      "01tTf000004RKTPIA4": "March 2026",
      "01tTf000004RRwHIAW": "September 2026",
      "01t6700000E4lsAAAR": "September 2025",
      "01tTf000004RKy6IAG": "September 2026",
      "01tTf000004RR6fIAG": "March 2026",
      "01t6700000E4lvYAAR": "September 2025",
      "01t6700000E4lvdAAB": "September 2025",
      "01t6700000E4lviAAB": "September 2025",
      "01tTf000004RIeUIAW": "March 2026",
      "01tTf000004RKRnIAO": "March 2026",
      "01tTf000004RLXXIA4": "March 2026",
      "01tTf000004RMGgIAO": "September 2026",
      "01tTf000004RSxBIAW": "September 2026",
      "01tTf000004RSynIAG": "September 2026",
      "01tTf00000270YRIAY": "September 2025",
      "01tTf0000028bUIIAY": "September 2025",
      "01tTf0000028cWnIAI": "September 2025",
      "01tTf000004RO8oIAG": "September 2026",
      "01tTf000004RQ5lIAG": "March 2026",
      "01tTf000004RQ7NIAW": "March 2026",
      "01tTf000004RQ8zIAG": "March 2026",
      "01tTf000004RQAbIAO": "September 2026",
      "01tTf000004RQCDIA4": "September 2026",
      "01tTf0000028cYPIAY": "September 2025",
      "01tTf0000028ca1IAA": "September 2025",
      "01tTf0000028cbdIAA": "September 2025",
      "01tTf000004RJCTIA4": "September 2026",
      "01tTf000004RNfmIAG": "March 2026",
      "01tTf000004RQDpIAO": "March 2026",
      "01tTf000004RQFRIA4": "March 2026",
      "01tTf000004RQH3IAO": "September 2026",
      "01tTf000004RQIfIAO": "September 2026",
      "01t6700000E4lntAAB": "September 2025",
      "01t6700000E4lnyAAB": "September 2025",
      "01tTf000004ROYbIAO": "March 2026",
      "01tTf000004RObpIAG": "March 2026",
      "01tTf000004ROf3IAG": "September 2026",
      "01tTf000004ROgfIAG": "September 2026",
      "01t6700000E4lmWAAR": "September 2025",
      "01tTf000004RO8nIAG": "March 2026",
      "01tTf000004ROAPIA4": "September 2026",
      "01tTf000006M50oIAC": "March 2026",
      "01tTf000006M50pIAC": "September 2026",
      "01tTf000006MFmZIAW": "September 2025",
      "01t6700000E4llxAAB": "September 2025",
      "01tTf000004RNkbIAG": "March 2026",
      "01tTf000004RNmDIAW": "September 2026",
      "01t6700000E4ougAAB": "September 2025",
      "01tTf000004RNHZIA4": "March 2026",
      "01tTf000004RNJBIA4": "September 2026",
      "01tTf000002Hk71IAC": "September 2025",
      "01tTf000002Hk72IAC": "September 2025",
      "01tTf000002Hk73IAC": "September 2025",
      "01tTf000004ROOwIAO": "March 2026",
      "01tTf000004RSIrIAO": "March 2026",
      "01tTf000004RSKTIA4": "March 2026",
      "01tTf000004RSNhIAO": "September 2026",
      "01tTf000004RSPJIA4": "September 2026",
      "01tTf000004RSQvIAO": "September 2026",
      "01tTf000002H6PxIAK": "September 2025",
      "01tTf000004RJykIAG": "March 2026",
      "01tTf000004RPT3IAO": "September 2026",
      "01tTf000003BRklIAG": "September 2025",
      "01tTf000003BRkmIAG": "September 2025",
      "01tTf000003BRknIAG": "September 2025",
      "01tTf000004RNB9IAO": "September 2026",
      "01tTf000004RQKHIA4": "March 2026",
      "01tTf000004RQLtIAO": "March 2026",
      "01tTf000004RQNVIA4": "March 2026",
      "01tTf000004RQP7IAO": "September 2026",
      "01tTf000004RQQjIAO": "September 2026",
      "01tTf000003SzzuIAC": "September 2025",
      "01tTf000003SzzvIAC": "January 2026",
      "01tTf000004RTI9IAO": "June 2026",
      "01tTf000003SzzoIAC": "September 2025",
      "01tTf000003SzzpIAC": "January 2026",
      "01tTf000004RTJlIAO": "June 2026",
      "01tTf000003T000IAC": "September 2025",
      "01tTf000003T001IAC": "January 2026",
      "01tTf000004LJ3lIAG": "September 2025",
      "01tTf000004LJ8bIAG": "March 2026",
      "01tTf000004iBa9IAE": "September 2026",
      "01tTf000004QRiEIAW": "March 2026",
      "01tTf000004QS9dIAG": "September 2025",
      "01tTf000004iBVJIA2": "September 2026",
      "01tTf000004QS4nIAG": "September 2025",
      "01tTf000004QS6PIAW": "March 2026",
      "01tTf000004iBYXIA2": "September 2026",
      "01tTf000004VNcLIAW": "September 2025",
      "01tTf000004iBofIAE": "September 2026",
      "01tTf000005770KIAQ": "March 2026",
      "01tTf000004QSJJIA4": "September 2025",
      "01tTf000004i7OiIAI": "September 2026",
      "01tTf00000575pqIAA": "March 2026",
      "01tTf000004QRyLIAW": "September 2025",
      "01tTf000004QRzxIAG": "March 2026",
      "01tTf000004iBThIAM": "September 2026",
      "01tTf000004QSETIA4": "September 2025",
      "01tTf000004QSG5IAO": "March 2026",
      "01tTf000004iBWvIAM": "September 2026",
      "01tTf000004RJNeIAO": "March 2026",
      "01tTf000004RM5NIAW": "September 2025",
      "01tTf000004RM6zIAG": "September 2026",
      "01tTf000006Q3rdIAC": "September 2025",
      "01tTf000006Q3reIAC": "March 2026",
      "01tTf000006Q3tFIAS": "September 2026",
      "01tTf000006Q3tGIAS": "September 2025",
      "01tTf000006Q3urIAC": "March 2026",
      "01tTf000006Q3usIAC": "September 2026",
      "01tTf000006Q3N0IAK": "March 2026",
      "01tTf000006Q3N1IAK": "September 2026",
      "01tTf000006Q3q2IAC": "September 2025",
      "01tTf000006Q2qkIAC": "September 2026",
      "01tTf000006Q3wTIAS": "September 2025",
      "01tTf000006Q3wUIAS": "March 2026"
    }
  },

  transformationRules: {
    birthDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    submittedAt: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    examDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    firstEnrolmentDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    lastEnrolmentDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    dateOfEQHE: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    isPreviousHigherEducationEnrollment: {
      type: "boolean_conversion",
      true_values: ["1", "true", true],
      false_values: ["0", "false", false]
    },
    informationAccuracyAgreement: {
      type: "boolean_conversion",
      true_values: ["1", "true", true],
      false_values: ["0", "false", false]
    },
    dataPrivacyConsent: {
      type: "boolean_conversion",
      true_values: ["1", "true", true],
      false_values: ["0", "false", false]
    },
    isEnrolledInAnotherUniversity: {
      type: "boolean_conversion",
      true_values: ["1", "true", true],
      false_values: ["0", "false", false]
    },
    isCorrespondanceAddressDiffer: {
      type: "boolean_conversion",
      true_values: ["1", "true", true],
      false_values: ["0", "false", false]
    },
    duration: {
      type: "type_conversion",
      target_type: "number"
    },
     programDurationDisplayName: {
      type: "type_conversion",
      target_type: "number"
    },
    userId: {
      type: "type_conversion",
      target_type: "string"
    },
    sfProgramId: {
      type: "type_conversion",
      target_type: "string"
    }
  }
};
