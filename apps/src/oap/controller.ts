import {
  Controller,
  Get,
  Query,
  HttpException,
  HttpStatus,
  Post,
  Body,
  Delete,
  Req,
  BadRequestException,
  InternalServerErrorException,
  Headers,
  Param,
} from '@nestjs/common';
import { OapService } from './service';
import { Request } from 'express';
import {
  RecaptchaVerificationDto,
  UserMigrationRequestDto,
  UserMigrationResponseDto,
  ApplicationMigrationRequestDto,
  ApplicationMigrationResponseDto
} from './dto/legacy-user.dto';

@Controller('oap')
export class OapController {
  constructor(private readonly oapService: OapService) {}
  @Get()
  async getByOap(
    @Query('name') name: string,
    @Query('mode') mode: string,
    @Query('language') language: string,
  ): Promise<any> {
    try {
      if (name && mode && name != undefined && mode != undefined) {
        const getOapDetails = await this.oapService.getOapDetails(
          name,
          mode,
          language,
        );
        return getOapDetails?.Item;
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/forms')
  async getByForm(
    @Query('oap') oap: string,
    @Query('mode') mode: string,
    @Query('form') form: string,
    @Query('language') language: string,
  ): Promise<any> {
    try {
      if (
        oap &&
        mode &&
        form &&
        oap != undefined &&
        mode != undefined &&
        form != undefined
      ) {
        const oapFormDetails = await this.oapService.getOapFormsDetails(
          oap,
          form,
          mode,
          language,
        );
        return oapFormDetails?.Item;
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/form/sections')
  async getBySection(
    @Query('oap') oap: string,
    @Query('mode') mode: string,
    @Query('formName') formName: string,
    @Query('sectionName') sectionName: string,
    @Query('language') language: string,
  ): Promise<any> {
    try {
      if (
        oap &&
        mode &&
        formName &&
        sectionName &&
        oap != undefined &&
        mode != undefined &&
        formName != undefined &&
        sectionName != undefined
      ) {
        const sectionDetails = await this.oapService.getOapFormSectionsDetails(
          oap,
          mode,
          formName,
          sectionName,
          language,
        );
        return sectionDetails?.Item;
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/sections')
  async getSectionByform(
    @Query('oapName') oapName: string,
    @Query('formName') formName: string,
    @Query('mode') mode: string,
    @Query('language') language: string,
  ): Promise<any> {
    try {
      if (
        oapName &&
        formName &&
        mode &&
        oapName != undefined &&
        formName != undefined &&
        mode != undefined
      ) {
        const sectionDetails = await this.oapService.getSections(
          oapName,
          formName,
          mode,
          language,
        );
        return sectionDetails;
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/getlegacyapplicationdata')
  async getLegacyApplicationData(
    @Query('programId') programId: string,
    @Query('oapName') oapName: string,
    @Query('mode') mode: string,
    @Query('termId') termId: string,
    @Headers('x-api-key') apikey: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      if (
        oapName &&
        programId &&
        mode &&
        termId &&
        apikey &&
        oapName != undefined &&
        programId != undefined &&
        mode != undefined &&
        termId != undefined &&
        apikey != undefined
      ) {
        return await this.oapService.getLegacyApplicationOapDetails(
          oapName,
          mode,
          programId,
          termId,
          apikey,
          request,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/studentinfo')
  async getStudentInfo(
    @Query('oapName') oapName: string,
    @Query('email') email: string,
    @Headers('x-api-key') APIKEY: string,
  ): Promise<any> {
    try {
      if (oapName && email && oapName != undefined && email != undefined) {
        return await this.oapService.getStudentInfo(oapName, email);
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/savestudentinfo')
  async saveStudentInfo(
    @Query('oapName') oapName: string,
    @Body() studentDetail: any,
    @Headers('x-api-key') APIKEY: string,
  ): Promise<any> {
    try {
      if (
        oapName &&
        studentDetail &&
        oapName != undefined &&
        studentDetail != undefined
      ) {
        return await this.oapService.saveStudentInfo(
          oapName,
          studentDetail,
          APIKEY,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/savestudentdetails') //this function need to be removed
  async saveStudentDetails(
    @Query('oapName') oapName: string,
    @Query('mode') mode: string,
    @Body() oapDetail: any,
    @Req() request: Request,
    @Headers('x-api-key') APIKEY: string,
  ): Promise<any> {
    try {
      if (
        oapName &&
        mode &&
        oapDetail &&
        APIKEY &&
        oapName != undefined &&
        mode != undefined &&
        oapDetail != undefined &&
        APIKEY != undefined
      ) {
        return await this.oapService.saveOapApplicantDetails(
          oapName,
          mode,
          oapDetail,
          request,
          APIKEY,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/getstudentdetails') //this function need to be removed
  async getStudentDetails(
    @Query('oapName') oapName: string,
    @Query('email') email: string,
    @Query('applicationId') applicationId: string,
  ): Promise<any> {
    try {
      if (
        oapName &&
        email &&
        applicationId &&
        oapName != undefined &&
        email != undefined &&
        applicationId != undefined
      ) {
        return await this.oapService.getOapApplicantDetails(
          oapName,
          email,
          applicationId,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/getstudentdetailsbyid') //this function need to be removed
  async getStudentDetailsById(
    @Query('oapName') oapName: string,
    @Query('applicationId') applicationId: string,
    @Query('agentContactUserId') agentContactUserId: string,
  ): Promise<any> {
    try {
      if (
        oapName &&
        applicationId &&
        agentContactUserId &&
        oapName != undefined &&
        applicationId != undefined &&
        agentContactUserId != undefined
      ) {
        return await this.oapService.getStudentDetailsById(
          oapName,
          applicationId,
          agentContactUserId,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw error;
    }
  }

  @Post('/savestudentapplicationdetails')
  async saveOapDetails(
    @Query('oapName') oapName: string,
    @Query('mode') mode: string,
    @Body() oapDetail: any,
    @Req() request: Request,
    @Headers('x-api-key') APIKEY: string,
  ): Promise<any> {
    try {
      if (
        oapName &&
        mode &&
        oapDetail &&
        APIKEY &&
        oapName != undefined &&
        mode != undefined &&
        oapDetail != undefined &&
        APIKEY != undefined
      ) {
        return await this.oapService.saveOapApplicantDetails(
          oapName,
          mode,
          oapDetail,
          request,
          APIKEY,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/getstudentapplicationdetails')
  async getOapDetails(
    @Query('oapName') oapName: string,
    @Query('email') email: string,
    @Query('applicationId') applicationId: string,
  ): Promise<any> {
    try {
      if (
        oapName &&
        email &&
        applicationId &&
        oapName != undefined &&
        email != undefined &&
        applicationId != undefined
      ) {
        return await this.oapService.getOapApplicantDetails(
          oapName,
          email,
          applicationId,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/getstudentapplicationdetailsbyid')
  async getOapDetailsById(
    @Query('oapName') oapName: string,
    @Query('applicationId') applicationId: string,
    @Query('agentContactUserId') agentContactUserId: string,
  ): Promise<any> {
    try {
      if (
        oapName &&
        applicationId &&
        agentContactUserId &&
        oapName != undefined &&
        applicationId != undefined &&
        agentContactUserId != undefined
      ) {
        return await this.oapService.getStudentDetailsById(
          oapName,
          applicationId,
          agentContactUserId,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw error;
    }
  }
  @Get('/getstudentapplications')
  async getStudentsOapApplications(
    @Query('email') email: string,
    @Query('brand') brand: string,
    @Query('language') language?: string,
  ): Promise<any> {
    try {
      if (email) {
        return await this.oapService.getStudentsOapApplications(
          email,
          brand,
          language,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/picklist/:fieldName')
  async getPicklist(
    @Param('fieldName') fieldName: string,
    @Query('brand') brand?: string,
    @Query('displayLanguage') displayLanguage?: string,
    @Query('filterParams') filterParams?: string,
  ) {
    return this.oapService.getPicklistData(
      fieldName,
      brand,
      displayLanguage,
      filterParams,
    );
  }
  @Post('/uploadstudentdocument/getsignedurl')
  async uploadDocuments(
    @Body() docDetails: any,
    @Req() request: Request,
  ): Promise<string> {
    try {
      if (
        docDetails.applicationId &&
        docDetails.documentType &&
        docDetails.documentName &&
        docDetails.contentType &&
        docDetails.applicationId != undefined &&
        docDetails.documentType != undefined &&
        docDetails.documentName != undefined &&
        docDetails.contentType != undefined
      ) {
        return await this.oapService.getSignedUrlToUploadDoc(
          docDetails,
          request,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      console.error('Error on file upload:', error);
      throw error;
    }
  }
  @Post('/uploadstudentdocument')
  async updateDocuments(
    @Body() docDetails: any,
    @Req() request: Request,
  ): Promise<string> {
    try {
      if (
        docDetails.applicationId &&
        docDetails.oapName &&
        docDetails.email &&
        docDetails.documentType &&
        docDetails.documentFormat &&
        docDetails.documentName &&
        docDetails.applicationId != undefined &&
        docDetails.oapName != undefined &&
        docDetails.email != undefined &&
        docDetails.documentType != undefined &&
        docDetails.documentFormat != undefined &&
        docDetails.documentName != undefined
      ) {
        return await this.oapService.uploadDocToDb(docDetails, request);
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      console.error('Error on file upload:', error);
      throw error;
    }
  }
  @Get('/gets3filedata')
  async getS3FileData(
    @Query('fileKey') fileKey: string,
    @Req() request: Request,
    @Query('s3BucketName') s3BucketName?: string,
  ): Promise<string> {
    try {
      console.log('s3BucketName', s3BucketName);
      return await this.oapService.getS3FileData(
        fileKey,
        s3BucketName,
        request,
      );
    } catch (error) {
      console.error('Error :', error);
      throw error;
    }
  }
  @Post('/opportunityfiles/upload')
  async uploadFile(@Body() event, @Req() request: Request) {
    try {
      return await this.oapService.uploadOpportunityFiles(event, request);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Delete('/deletestudentdocument')
  async deleteFile(
    @Query('email') email: string,
    @Query('oapName') oapName: string,
    @Query('type') type: string,
    @Query('applicationId') applicationId: string,
    @Query('name') name: string,
    @Query('documentId') documentId: string,
  ): Promise<string> {
    try {
      if (
        email &&
        oapName &&
        type &&
        applicationId &&
        name &&
        documentId &&
        email != undefined &&
        oapName != undefined &&
        type != undefined &&
        applicationId != undefined &&
        name != undefined &&
        documentId != undefined
      ) {
        return await this.oapService.deleteFile(
          email,
          oapName,
          type,
          applicationId,
          name,
          documentId,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      console.error('Error on file deletion:', error);
      throw new InternalServerErrorException(
        'Error on file deletion. Please contact administrator.',
      );
    }
  }
  @Get('/getstudentdocument')
  async getDocs(
    @Query('email') email: string,
    @Query('oapName') oapName: string,
    @Query('type') type: string,
    @Query('applicationId') applicationId: string,
    @Query('documentId') documentId?: string,
  ): Promise<string> {
    try {
      if (email && oapName && email != undefined && oapName != undefined) {
        return await this.oapService.getStudentsDocuments(
          email,
          oapName,
          type,
          applicationId,
          documentId,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      console.error('Error on file upload:', error);
      throw error;
    }
  }
  @Get('/appid')
  async dataFixForAppId(@Req() request: Request): Promise<any> {
    try {
      return await this.oapService.updateAppIdOldRecord(request);
    } catch (error) {
      console.error('Error on file upload:', error);
      throw error;
    }
  }
  @Post('/addstudentformsectionsdata')
  async addStudentFormSectionsData(
    @Query('oapName') oapName: string,
    @Query('mode') mode: string,
  ): Promise<any> {
    try {
      return await this.oapService.addStudentFormSectionsData(oapName, mode);
    } catch (error) {
      console.error('Error on file upload:', error);
      throw error;
    }
  }
  @Post('/testappid')
  async testappid(@Body() details: any): Promise<any> {
    try {
      return await this.oapService.makeApiCalls(details);
    } catch (error) {
      console.error('Error on file upload:', error);
      throw error;
    }
  }

  @Get('/opportunity/:id')
  async getOpportunityById(
    @Param('id') id: string,
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      if (!id) {
        throw new BadRequestException('Opportunity ID is required');
      }
      return await this.oapService.getOpportunityById(id, APIKEY, request);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch opportunity',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('/submitchangerequest')
  async submitChangeRequestForm(
    @Query('oap') oap: string,
    @Query('mode') mode: string,
    @Body() requestData: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      if (
        !oap ||
        !mode ||
        !requestData.formType ||
        !requestData.opportunityId
      ) {
        throw new BadRequestException('Missing required fields');
      }
      return await this.oapService.submitChangeRequestForm(
        requestData,
        oap,
        mode,
        request,
      );
    } catch (error) {
      console.error('Error submitting change request form:', error);
      throw error;
    }
  }

  @Post('/verify-recaptcha')
  async verifyRecaptcha(
    @Body() recaptchaData: RecaptchaVerificationDto,
  ): Promise<any> {
    try {
      const { token, action } = recaptchaData;

      if (!token) {
        throw new BadRequestException('reCAPTCHA token is required');
      }

      if (!action) {
        throw new BadRequestException('Action is required');
      }

      return await this.oapService.verifyRecaptcha(token, action);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'reCAPTCHA verification failed',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('/migratelegacyusers')
  async migrateUsers(
    @Body() migrationRequest: UserMigrationRequestDto,
    @Headers('x-api-key') APIKEY: string,
    @Query('batchSize') batchSize?: number,
  ): Promise<UserMigrationResponseDto> {
    try {
      const { oapName, mode } = migrationRequest;

      if (!oapName || !mode) {
        throw new BadRequestException('oapName and mode are required');
      }

      if (!APIKEY) {
        throw new BadRequestException('API key is required');
      }

      // Validate mode is "STUDENT" as specified in requirements
      if (mode !== 'STUDENT') {
        throw new BadRequestException('Only STUDENT mode is supported for user migration');
      }

      // Validate and set batch size (default 15, max 25 to respect AWS limits)
      const validatedBatchSize = Math.min(Math.max(batchSize || 15, 1), 25);

      if (batchSize && batchSize !== validatedBatchSize) {
        console.warn(`Batch size adjusted from ${batchSize} to ${validatedBatchSize} (min: 1, max: 25)`);
      }

      return await this.oapService.processUserMigration(oapName, mode, validatedBatchSize);
    } catch (error) {
      console.error('Error in user migration:', error);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new HttpException(
        error.message || 'User migration failed',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('/migrateapplications')
  async migrateApplications(
    @Body() migrationRequest: ApplicationMigrationRequestDto,
    @Headers('x-api-key') APIKEY: string,
    @Query('batchSize') batchSize?: number,
  ): Promise<ApplicationMigrationResponseDto> {
    try {
      const { oapName, mode, dryRun } = migrationRequest;

      if (!oapName || !mode) {
        throw new BadRequestException('oapName and mode are required');
      }

      if (!APIKEY) {
        throw new BadRequestException('API key is required');
      }

      // Validate mode is "STUDENT" as specified in requirements
      if (mode !== 'STUDENT') {
        throw new BadRequestException('Only STUDENT mode is supported for application migration');
      }

      // Validate and set batch size (default 10, max 15 for application processing)
      const validatedBatchSize = Math.min(Math.max(batchSize || 10, 1), 15);

      if (batchSize && batchSize !== validatedBatchSize) {
        console.warn(`Batch size adjusted from ${batchSize} to ${validatedBatchSize} (min: 1, max: 15)`);
      }

      return await this.oapService.processApplicationMigration(
        oapName,
        mode,
        validatedBatchSize,
        dryRun || false
      );
    } catch (error) {
      console.error('Application migration failed:', error);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new HttpException(
        error.message || 'Application migration failed',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
