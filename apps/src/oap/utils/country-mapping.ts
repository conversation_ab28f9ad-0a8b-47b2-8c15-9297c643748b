/**
 * Comprehensive country name to ISO code mapping
 * This mapping can be reused across all country-related fields
 */

/**
 * Transform country display name to country code using service method
 * @param countryName - Full country name
 * @param getCountryCodeFn - Function to get country code from service
 * @returns ISO country code or original name if not found
 */
export async function transformCountryNameToCode(
  countryName: string,
  getCountryCodeFn: (name: string) => Promise<string>
): Promise<string> {
  if (!countryName || typeof countryName !== 'string') {
    return countryName;
  }

  try {
    const countryCode = await getCountryCodeFn(countryName);
    return countryCode || countryName;
  } catch (error) {
    console.warn(`Error getting country code for ${countryName}:`, error);
    return countryName;
  }
}

/**
 * Transform country fields according to naming convention:
 * - Fields ending with "DisplayName" keep full country names
 * - Fields without "DisplayName" get transformed to country codes
 * @param data - Object containing country fields
 * @param getCountryCodeFn - Function to get country code from service
 * @returns Object with transformed country fields
 */
export async function transformCountryFields(
  data: any,
  getCountryCodeFn: (name: string) => Promise<string>
): Promise<any> {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const transformedData = { ...data };

  // Define country field mappings - support both single fields and arrays
  const countryFieldMappings = [
    // Fields that need both DisplayName (full name) and code versions
    { displayField: 'countryDisplayName', codeField: 'country' },
    { displayField: 'citizenshipDisplayName', codeField: 'citizenship' },
    { displayField: 'correspondenceCountryDisplayName', codeField: 'correspondenceCountry' },
    { displayField: 'awardingCountryDisplayName', codeField: 'awardingCountry' },
    { displayField: 'countryOfInitialRegistrationDisplayName', codeField: 'countryOfInitialRegistration' },
    { displayField: 'countryOfEQHEDisplayName', codeField: 'countryOfEQHE' },
  ];

  // Process country field mappings
  for (const mapping of countryFieldMappings) {
    const displayValue = transformedData[mapping.displayField];
    const codeValue = transformedData[mapping.codeField];

    // If we have a display field value, ensure both display and code fields are set
    if (displayValue && typeof displayValue === 'string') {
      // Keep the display field as full country name
      transformedData[mapping.displayField] = displayValue;

      // Set the code field to country code
      try {
        const countryCode = await transformCountryNameToCode(displayValue, getCountryCodeFn);
        transformedData[mapping.codeField] = countryCode;
      } catch (error) {
        console.warn(`Error transforming country code for ${displayValue}:`, error);
        transformedData[mapping.codeField] = displayValue; // Fallback to original value
      }
    }
    // If we only have a code field value, try to set both fields
    else if (codeValue && typeof codeValue === 'string' && !displayValue) {
      // Assume the code field contains the full country name and needs transformation
      transformedData[mapping.displayField] = codeValue; // Keep as display name

      try {
        const countryCode = await transformCountryNameToCode(codeValue, getCountryCodeFn);
        transformedData[mapping.codeField] = countryCode;
      } catch (error) {
        console.warn(`Error transforming country code for ${codeValue}:`, error);
        transformedData[mapping.codeField] = codeValue; // Keep original value
      }
    }
  }

  // Handle nested objects and arrays recursively
  for (const [key, value] of Object.entries(transformedData)) {
    if (Array.isArray(value)) {
      transformedData[key] = await Promise.all(
        value.map(item => transformCountryFields(item, getCountryCodeFn))
      );
    } else if (value && typeof value === 'object' && !countryFieldMappings.some(m => m.displayField === key || m.codeField === key)) {
      transformedData[key] = await transformCountryFields(value, getCountryCodeFn);
    }
  }

  return transformedData;
}
