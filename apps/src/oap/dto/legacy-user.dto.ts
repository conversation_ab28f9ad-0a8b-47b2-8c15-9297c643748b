import { IsEmail, IsNotEmpty, IsOptional, IsString, IsObject, ValidateNested, IsIn, IsBoolean, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

export class RecaptchaVerificationDto {
  @IsNotEmpty()
  @IsString()
  token: string;

  @IsNotEmpty()
  @IsString()
  action: string;
}

// User Migration DTOs
export class S3ConfigDto {
  @IsNotEmpty()
  @IsString()
  bucket: string;

  @IsNotEmpty()
  @IsString()
  folder_path: string;

  @IsNotEmpty()
  @IsString()
  file_name: string;

  @IsNotEmpty()
  @IsString()
  @IsIn(['xlsx', 'csv'])
  file_type: 'xlsx' | 'csv';
}

export class CognitoConfigDto {
  @IsNotEmpty()
  @IsString()
  user_pool_id: string;

  @IsNotEmpty()
  @IsString()
  region: string;
}

export class UserMigrationConfigDto {
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => S3ConfigDto)
  s3_config: S3ConfigDto;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CognitoConfigDto)
  cognito_config: CognitoConfigDto;

  @IsNotEmpty()
  @IsObject()
  column_mappings: Record<string, string>;
}

export class UserMigrationRequestDto {
  @IsNotEmpty()
  @IsString()
  oapName: string;

  @IsNotEmpty()
  @IsString()
  mode: string;
}

export class UserMigrationResultDto {
  email: string;
  status: 'success' | 'failed';
  error?: string;
  cognitoUserId?: string;
}

export class UserMigrationResponseDto {
  totalUsers: number;
  successfulMigrations: number;
  failedMigrations: number;
  results: UserMigrationResultDto[];
  configUsed: UserMigrationConfigDto;
}

// Application Migration DTOs
export class ApplicationS3ConfigDto {
  @IsNotEmpty()
  @IsString()
  bucket: string;

  @IsNotEmpty()
  @IsString()
  file_path: string;
}

export class ApplicationMigrationConfigDto {
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => ApplicationS3ConfigDto)
  s3_config: ApplicationS3ConfigDto;

  @IsNotEmpty()
  @IsString()
  brand: string;

  @IsNotEmpty()
  @IsObject()
  field_mappings: any; // Will be loaded from brand mapping files

  @IsNotEmpty()
  @IsObject()
  picklist_mappings: Record<string, Record<string, string>>;

  @IsObject()
  transformation_rules?: Record<string, any>;
}

export class ApplicationMigrationRequestDto {
  @IsNotEmpty()
  @IsString()
  oapName: string;

  @IsNotEmpty()
  @IsString()
  mode: string;

  @IsOptional()
  @IsNumber()
  batchSize?: number;

  @IsOptional()
  @IsBoolean()
  dryRun?: boolean;
}

export class ApplicationMigrationResultDto {
  applicationId: string;
  email: string;
  status: 'success' | 'failed' | 'skipped';
  error?: string;
  documentsSaved?: number;
  picklistFieldsMapped?: number;
  transformedData?: any;
}

export class ApplicationMigrationResponseDto {
  totalApplications: number;
  successfulMigrations: number;
  failedMigrations: number;
  skippedApplications: number;
  results: ApplicationMigrationResultDto[];
  configUsed: ApplicationMigrationConfigDto;
  processingTimeMs: number;
  documentsProcessed: number;
  picklistFieldsProcessed: number;
}

export class DocumentStorageResultDto {
  documentId: string;
  applicationId: string;
  documentType: string;
  status: 'success' | 'failed';
  error?: string;
  storageLocation?: string;
  key?: string;
}

export class PicklistMappingResultDto {
  fieldName: string;
  originalValue: string;
  mappedValue: string;
  status: 'success' | 'failed' | 'no_mapping_found';
  error?: string;
}
