# Application Migration API

This API provides a comprehensive solution for migrating legacy applications from S3 to the new student application database. It follows the blueprint pattern of:

1. **Fetch JSON from S3** - Retrieve legacy application data
2. **Transform Data** - Convert legacy format to new format using field mappings
3. **Apply Data Transformations** - Country code mapping, phone number formatting
4. **Save Documents** - Store document metadata in separate database with UUID references
5. **Map Picklist Fields** - Transform legacy picklist values to new values
6. **Save to Student Application DB** - Store final transformed data

## API Endpoint

```
POST /oap/migrateapplications
```

### Headers
- `x-api-key`: Required API key for authentication
- `Content-Type`: application/json

### Query Parameters
- `batchSize` (optional): Number of applications to process in each batch (default: 10, max: 15)

### Request Body

```json
{
  "oapName": "UEG",
  "mode": "STUDENT",
  "dryRun": false,
  "batchSize": 10
}
```

### Response

```json
{
  "totalApplications": 100,
  "successfulMigrations": 95,
  "failedMigrations": 3,
  "skippedApplications": 2,
  "results": [
    {
      "applicationId": "6d2eeb7c-452e-11f0-889a-fa8c60dfa742",
      "email": "<EMAIL>",
      "status": "success",
      "documentsSaved": 5,
      "picklistFieldsMapped": 8
    }
  ],
  "configUsed": { /* migration configuration */ },
  "processingTimeMs": 45000,
  "documentsProcessed": 475,
  "picklistFieldsProcessed": 800
}
```

## Configuration Setup

The migration system uses a hybrid configuration approach:

### 1. S3 Configuration (Database)

S3 configuration is stored in the existing OAP details table (`process.env.GUS_OAP_TABLE`) with:
- **PK**: `{oapName}`
- **SK**: `{mode}`

Add these fields to your OAP configuration:
```json
{
  "PK": "UEG",
  "SK": "STUDENT",
  "brand": "UEG",
  "migrationS3Bucket": "campusnet-sf-sync-stage",
  "migrationS3Path": "legacy-applications/ueg",
  // ... other OAP configuration
}
```

### 2. Field Mappings (Project Files)

Field mappings are stored as TypeScript files in the project under `src/oap/mappings/`:

**File Structure:**
```
src/oap/mappings/
├── index.ts              # Registry of all brand mappings
├── ueg-mappings.ts       # UEG-specific mappings
├── ibat-mappings.ts      # IBAT-specific mappings
└── {brand}-mappings.ts   # Other brand mappings
```

**Example Brand Mapping File (`ueg-mappings.ts`):**
```typescript
export const UEG_APPLICATION_MAPPINGS: ApplicationMappingConfig = {
  directFields: {
    "data.firstName": "firstName",
    "data.legalFamilyName": "lastName",
    "data.personalEmail": "email",
    // ... more field mappings
  },
  documentFields: {
    "data.fileCV": "CV",
    "data.photo": "photograph",
    // ... document mappings
  },
  arrayFields: {
    "data.educationSchools": "educationInfo",
    "data.eqhe": "eqheDetails"
  },
  subsectionMappings: {
    educationInfo: {
      "educationSchoolsType": "educationDetailsEntryType",
      "educationSchoolsStudyStatus": "educationStatus"
      // ... subsection mappings
    }
  },
  picklistMappings: {
    level: {
      "Undergraduate": "Undergraduate",
      "Postgraduate": "Postgraduate"
    }
  },
  transformationRules: {
    birthDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    }
  }
};
```

### 3. Adding New Brand Mappings

To add a new brand:

1. Create a new mapping file: `src/oap/mappings/{brand}-mappings.ts`
2. Export the mapping configuration following the `ApplicationMappingConfig` interface
3. Register it in `src/oap/mappings/index.ts`:
   ```typescript
   import { NEW_BRAND_MAPPINGS } from './new-brand-mappings';

   export const BRAND_MAPPINGS = {
     'UEG': UEG_APPLICATION_MAPPINGS,
     'IBAT': IBAT_APPLICATION_MAPPINGS,
     'NEW_BRAND': NEW_BRAND_MAPPINGS,
   };
   ```

## Data Flow

### 1. Legacy Application Structure
```json
{
  "_id": "6846d56747ab090740059b32",
  "uuid": "6d2eeb7c-452e-11f0-889a-fa8c60dfa742",
  "userId": 761,
  "data": {
    "firstName": "John",
    "legalFamilyName": "Doe",
    "personalEmail": "<EMAIL>",
    "fileCV": [
      {
        "originalFileName": "cv.pdf",
        "filePath": "uuid/CV/hash.pdf"
      }
    ],
    "educationSchools": [
      {
        "educationSchoolsType": "graduation",
        "educationSchoolsDegree": "Bachelor"
      }
    ]
  }
}
```

### 2. Transformed Application Structure
```json
{
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "educationInfo": [
    {
      "educationDetailsEntryType": "graduation",
      "degree": "Bachelor"
    }
  ],
  "migrationMetadata": {
    "migratedAt": "2025-01-07T10:00:00.000Z",
    "sourceSystem": "legacy_s3",
    "migrationVersion": "1.0",
    "originalId": "6846d56747ab090740059b32",
    "originalUuid": "6d2eeb7c-452e-11f0-889a-fa8c60dfa742"
  }
}
```

## S3 File Organization

Legacy applications should be organized in S3 folders:

```
s3://campusnet-sf-sync-stage/
├── legacy-applications/
│   ├── ueg/
│   │   └── applications.json
│   ├── ibat/
│   │   └── applications.json
│   └── {brand}/
│       └── applications.json
```

The service will look for `applications.json` in the specified folder path.

## Data Transformations

The migration service applies several automatic data transformations:

### 1. Country Code Mapping
Country fields follow a specific naming convention:
- **Fields ending with "DisplayName"** (e.g., `countryDisplayName`, `citizenshipDisplayName`) contain **full country names** (e.g., "United States", "India")
- **Fields without "DisplayName"** (e.g., `country`, `citizenship`) contain **ISO country codes** (e.g., "US", "IN")

The system automatically creates both versions:
```json
// Input: "citizenship": "United States"
// Output:
{
  "citizenshipDisplayName": "United States",  // Full name
  "citizenship": "US"                         // ISO code
}
```

### 2. Phone Number Formatting
Phone number fields are converted from strings to structured objects:
```json
// Input: "+************"
// Output:
{
  "number": "291111111",
  "numberWithCode": "+************",
  "dialCode": "375",
  "countryCode": "by"
}
```

### 3. Document ID Generation
Document IDs are generated as proper UUIDs instead of timestamp-based IDs:
```json
{
  "CV": [{"documentId": "48051a1a-b031-4e45-ae46-728add8b7177"}],
  "photograph": [{"documentId": "deef8db5-cd88-40ce-a466-10cdf28c3700"}]
}
```

### 4. List Mapping
Single source fields can be mapped to multiple target fields:
```json
// Configuration: "data.title": ["title", "titleDisplayName"]
// Input: "data.title": "dr"
// Output: {"title": "dr", "titleDisplayName": "dr"}
```

### 5. Type Conversion
Automatic type conversion based on transformation rules:
- String to Number: `"12"` → `12`
- String to Boolean: `"1"` → `true`
- Number to String: `761` → `"761"`

### 6. Boolean Conversion
String boolean values are converted to actual booleans based on transformation rules:
- `"1"`, `"true"`, `true` → `true`
- `"0"`, `"false"`, `false` → `false`

See [TRANSFORMATION_EXAMPLES.md](./TRANSFORMATION_EXAMPLES.md) for detailed examples.

## Database Tables

### Student Applications
- **Table**: `process.env.STUDENT_DETAILS`
- **PK**: `{email}`
- **SK**: `{oapName}_{applicationId}`

### Documents
- **Table**: `process.env.STUDENT_DOCUMENTS`
- **PK**: `{email}`
- **SK**: `{applicationId}_{documentType}_{documentId}`

### OAP Configuration (includes S3 config)
- **Table**: `process.env.GUS_OAP_TABLE`
- **PK**: `{oapName}`
- **SK**: `{mode}`

## Error Handling

The API includes comprehensive error handling:
- Individual application failures don't stop batch processing
- Detailed error reporting for each failed application
- Automatic retry mechanisms for transient failures
- Rollback capabilities for critical failures

## Usage Examples

### Basic Migration
```bash
curl -X POST "https://api.example.com/oap/migrateapplications" \
  -H "x-api-key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "oapName": "UEG",
    "mode": "STUDENT"
  }'
```

### Dry Run
```bash
curl -X POST "https://api.example.com/oap/migrateapplications" \
  -H "x-api-key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "oapName": "UEG", 
    "mode": "STUDENT",
    "dryRun": true
  }'
```

### Custom Batch Size
```bash
curl -X POST "https://api.example.com/oap/migrateapplications?batchSize=5" \
  -H "x-api-key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "oapName": "UEG",
    "mode": "STUDENT"
  }'
```
