# Migration Result Storage System

## Overview

For processing 85k+ migration records efficiently, the system now implements a scalable storage solution that separates successful and failed applications into different files and provides comprehensive tracking.

## Storage Architecture

### File Structure
```
s3://migration-results-bucket/
└── migration-results/
    └── {oapName}/
        └── {migrationId}/
            ├── successful-applications-{timestamp}.jsonl
            ├── failed-applications-{timestamp}.jsonl
            └── migration-summary-{timestamp}.json
```

### File Formats

#### 1. Successful Applications (JSONL)
- **Format**: JSON Lines (one JSON object per line)
- **Content**: Successful migration results with minimal data
- **Size Optimization**: Large `transformedData` is summarized to save space

```jsonl
{"applicationId":"app-123","email":"<EMAIL>","status":"success","documentsSaved":3,"picklistFieldsMapped":5,"hasTransformedData":true,"transformedDataSize":2048}
{"applicationId":"app-124","email":"<EMAIL>","status":"success","documentsSaved":2,"picklistFieldsMapped":3,"hasTransformedData":true,"transformedDataSize":1856}
```

#### 2. Failed Applications (JSONL)
- **Format**: JSON Lines
- **Content**: Failed migration results with error details

```jsonl
{"applicationId":"app-125","email":"<EMAIL>","status":"failed","error":"Missing required field: email"}
{"applicationId":"app-126","email":"<EMAIL>","status":"failed","error":"Document processing failed: Invalid file format"}
```

#### 3. Migration Summary (JSON)
- **Format**: Single JSON object
- **Content**: Complete migration statistics and metadata

```json
{
  "migrationId": "UEG_STUDENT_1704067200000",
  "oapName": "UEG",
  "timestamp": "2024-01-01T00-00-00-000Z",
  "totalApplications": 85000,
  "processedApplications": 85000,
  "successfulMigrations": 82500,
  "failedMigrations": 2500,
  "skippedApplications": 0,
  "successRate": "97.06%",
  "files": {
    "successfulApplications": "migration-results/UEG/UEG_STUDENT_1704067200000/successful-applications-2024-01-01T00-00-00-000Z.jsonl",
    "failedApplications": "migration-results/UEG/UEG_STUDENT_1704067200000/failed-applications-2024-01-01T00-00-00-000Z.jsonl",
    "summary": "migration-results/UEG/UEG_STUDENT_1704067200000/migration-summary-2024-01-01T00-00-00-000Z.json"
  },
  "processingStats": {
    "totalDocumentsProcessed": 247500,
    "totalPicklistFieldsProcessed": 412500
  },
  "completedAt": "2024-01-01T02:15:30.000Z"
}
```

## Performance Optimizations

### 1. Streaming Storage
- Results are written to S3 in batches during processing
- No need to keep all results in memory
- Supports processing unlimited number of records

### 2. JSONL Format Benefits
- Line-by-line processing capability
- Easy to parse and analyze
- Efficient for large datasets
- Can be processed with standard tools (jq, awk, etc.)

### 3. Size Optimization
- Successful applications store only summary data
- Large `transformedData` objects are not stored in result files
- Metadata indicates if transformed data was generated

### 4. Separate Success/Failure Files
- Easy to identify and process failed applications
- Successful applications can be processed independently
- Reduces file size for each category

## Usage Examples

### API Response
```json
{
  "migrationId": "UEG_STUDENT_1704067200000",
  "totalApplications": 85000,
  "successfulMigrations": 82500,
  "failedMigrations": 2500,
  "skippedApplications": 0,
  "processingTimeMs": 7890000,
  "documentsProcessed": 247500,
  "picklistFieldsProcessed": 412500,
  "resultFiles": {
    "successfulApplications": "s3://gus-migration-results/migration-results/UEG/UEG_STUDENT_1704067200000/successful-applications-2024-01-01T00-00-00-000Z.jsonl",
    "failedApplications": "s3://gus-migration-results/migration-results/UEG/UEG_STUDENT_1704067200000/failed-applications-2024-01-01T00-00-00-000Z.jsonl",
    "migrationSummary": "s3://gus-migration-results/migration-results/UEG/UEG_STUDENT_1704067200000/migration-summary-2024-01-01T00-00-00-000Z.json"
  },
  "message": "Migration completed. Results stored in S3. Check the summary file for detailed information."
}
```

### Processing Failed Applications
```bash
# Download failed applications file
aws s3 cp s3://gus-migration-results/migration-results/UEG/UEG_STUDENT_1704067200000/failed-applications-2024-01-01T00-00-00-000Z.jsonl ./failed-apps.jsonl

# Count failed applications by error type
cat failed-apps.jsonl | jq -r '.error' | sort | uniq -c | sort -nr

# Extract specific failed application IDs
cat failed-apps.jsonl | jq -r '.applicationId' > failed-app-ids.txt
```

### Processing Successful Applications
```bash
# Download successful applications file
aws s3 cp s3://gus-migration-results/migration-results/UEG/UEG_STUDENT_1704067200000/successful-applications-2024-01-01T00-00-00-000Z.jsonl ./success-apps.jsonl

# Count applications by document count
cat success-apps.jsonl | jq '.documentsSaved' | sort -n | uniq -c

# Extract emails of successful applications
cat success-apps.jsonl | jq -r '.email' > successful-emails.txt
```

## Configuration

### Environment Variables
```bash
# S3 bucket for storing migration results
MIGRATION_RESULTS_BUCKET=gus-migration-results

# Optional: Custom bucket for specific environments
MIGRATION_RESULTS_BUCKET_STAGE=gus-migration-results-stage
MIGRATION_RESULTS_BUCKET_PROD=gus-migration-results-prod
```

### Batch Processing Settings
- **Default Batch Size**: 10 applications per batch
- **Maximum Batch Size**: 15 applications per batch
- **Delay Between Batches**: 1000ms (1 second)
- **Storage Frequency**: After each batch completion

## Benefits for 85k Records

1. **Memory Efficiency**: Only current batch held in memory
2. **Progress Tracking**: Real-time progress updates during processing
3. **Fault Tolerance**: Batch failures don't stop entire migration
4. **Easy Analysis**: Separate files for success/failure analysis
5. **Scalability**: Can handle unlimited number of records
6. **Cost Effective**: Optimized storage format reduces S3 costs
7. **Monitoring**: Comprehensive statistics and metadata
8. **Recovery**: Failed batches can be identified and reprocessed

## Monitoring and Alerts

The system provides detailed logging and can be integrated with monitoring systems:
- Progress updates every batch
- Error logging for failed batches
- Final summary with complete statistics
- S3 file locations for result analysis
