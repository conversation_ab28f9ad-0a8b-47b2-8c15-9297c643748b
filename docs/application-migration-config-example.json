{"PK": "APPLICATION_MIGRATION", "SK": "UEG_STUDENT_APPLICATION_MIGRATION", "s3_config": {"bucket": "campusnet-sf-sync-stage", "file_name": "legacy-applications/ueg-applications.json", "file_type": "json"}, "field_mappings": {"directFields": {"data.firstName": "firstName", "data.legalFamilyName": "lastName", "data.personalEmail": "email", "data.dateOfBirth": "birthDate", "data.placeOfBirth": "placeOfBirth", "data.countryOfBirth": "countryDisplayName", "data.citizenship": "citizenshipDisplayName", "data.passportNumber": "passportNumber", "data.correspondenceLanguage": "correspondenceLanguage", "data.applyReferFriendScheme": "isReferFriend", "data.referIdNumber": "ueIDNumber", "data.referFirstName": "referralFirstName", "data.referSurname": "referralLastName", "data.declarationInfoTrue": "informationAccuracyAgreement", "data.personalInfoTrue": "dataPrivacyConsent", "data.higherEducationExits": "isPreviousHigherEducationEnrollment", "data.educationMainCountry": "countryOfInitialRegistration", "data.educationMainGermanUniversity": "universityOfInitialRegistration", "data.educationMainName": "otherUniversityName", "data.educationMainSemesterYearMonth": "semesterOfInitialRegistration", "data.educationAnotherUniversityEnrolledGermany": "isEnrolledInAnotherUniversity", "data.correspondenceStreetAddress": "correspondenceStreetAddress", "data.correspondenceCity": "correspondenceCity", "data.correspondenceState": "correspondenceState", "data.correspondenceCountry": "correspondenceCountryDisplayName", "data.correspondencePostalCode": "correspondencePostalCode", "data.mailingAddress": "streetAddress", "data.city": "city", "data.country": "countryDisplayName", "data.state": "mailingState", "data.postalCode": "postalCode", "data.addressCo": "careOf", "data.correspondenceAddress": "isCorrespondanceA<PERSON><PERSON><PERSON><PERSON><PERSON>", "data.program": "program", "data.level": "programTypeDisplayName", "data.language": "language", "data.location": "location", "data.duration": "duration", "data.intake": "productId", "data.title": "title", "data.phoneHome": "phoneNumber", "data.phone": "otherNumber", "uuid": "applicationId", "userId": "legacyUserId", "sfProgramId": "sfProgramId", "submittedAt": "submittedAt", "createdAt": "createdAt", "updatedAt": "updatedAt", "isSubmitted": "isSubmitted"}, "documentFields": {"data.fileCV": "CV", "data.photo": "photograph", "data.educationSchoolsCertificateFile": "HEEQ", "data.educationSchoolsTranscriptFile": "finalHEEQ", "data.fileGermanTest": "languageCertificateGermen", "data.fileEnglishTest": "languageCertificateEnglish", "data.fileEntranceTest": "entranceTest", "data.filePortfolio": "portfolio", "data.applyReasonFile": "motivationInformation", "data.nonObjectionCertificate": "noObjectionCertificate", "data.schoolLeavingCertificate": "deregistrationCertificate", "data.otherFiles": "otherCertificate", "data.fileEducationQualification": "bachelorTranscript"}, "arrayFields": {"data.educationSchools": "educationInfo", "data.eqhe": "eqheDetails"}}, "subsection_mappings": {"educationInfo": {"educationSchoolsType": "educationDetailsEntryType", "educationSchoolsStudyStatus": "educationStatus", "educationSchoolsExamDate": "examDate", "educationSchoolsFirstEnrolmentYear": "firstEnrolmentDate", "educationSchoolsLastEnrolmentYear": "lastEnrolmentDate", "educationSchoolsSpecialisation": "programmeCompleted", "educationSchoolsDegree": "degree", "educationSchoolsSpecialisationName": "specialisation", "educationSchoolsPartnerUniversity": "partnerUniversityName", "educationSchoolsPartnerUniversityOther": "otherPartnerUniversityName", "educationSchoolsUniversity": "universityName", "educationSchoolsPresenceOrDistanceType": "attendanceType", "educationSchoolsStudyTimeType": "studyMode", "educationSchoolsGrade": "finalGrade", "educationSchoolsCountry": "awardingCountry", "educationSchoolsCity": "educationCity", "educationSchoolsDetails": "educationRemark"}, "eqheDetails": {"eqheDate": "dateOfEQHE", "eqheCity": "cityOfEQHE", "eqhecountry": "countryOfEQHE", "eqheTitle": "originalTitleOfEQHE"}}, "picklist_mappings": {"level": {"Undergraduate": "Undergraduate", "Postgraduate": "Postgraduate", "Doctorate": "Doctorate", "ShortCourses": "ShortCourses", "Diploma": "Diploma"}, "language": {"English": "English", "German": "German", "Spanish": "Spanish", "French": "French"}, "title": {"mr": "Mr", "mrs": "Mrs", "ms": "Ms", "dr": "Dr", "prof": "Prof"}}, "transformation_rules": {"birthDate": {"type": "date_format", "from_format": "ISO", "to_format": "YYYY-MM-DD"}, "submittedAt": {"type": "date_format", "from_format": "ISO", "to_format": "ISO"}, "isReferFriend": {"type": "boolean_conversion", "true_values": ["1", "true", true], "false_values": ["0", "false", false]}, "isPreviousHigherEducationEnrollment": {"type": "boolean_conversion", "true_values": ["1", "true", true], "false_values": ["0", "false", false]}}}