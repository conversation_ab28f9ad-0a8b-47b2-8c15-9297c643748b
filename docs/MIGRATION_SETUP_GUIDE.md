# Application Migration Setup Guide

This guide walks you through setting up the application migration system for a new brand.

## Step 1: Prepare S3 Data

1. **Upload Legacy Applications**: Place your legacy application JSON files in S3:
   ```
   s3://campusnet-sf-sync-stage/legacy-applications/{brand}/applications.json
   ```

2. **Verify JSON Structure**: Ensure your applications follow the expected legacy format:
   ```json
   [
     {
       "_id": "6846d56747ab090740059b32",
       "uuid": "6d2eeb7c-452e-11f0-889a-fa8c60dfa742",
       "userId": 761,
       "data": {
         "firstName": "John",
         "legalFamilyName": "Doe",
         "personalEmail": "<EMAIL>",
         // ... other fields
       }
     }
   ]
   ```

## Step 2: Update OAP Configuration

Add migration-specific fields to your OAP configuration in the `GUS_OAP_TABLE`:

```json
{
  "PK": "YOUR_OAP_NAME",
  "SK": "STUDENT",
  "brand": "YOUR_BRAND",
  "migrationS3Bucket": "campusnet-sf-sync-stage",
  "migrationS3Path": "legacy-applications/your-brand",
  // ... existing OAP configuration
}
```

**Required Fields:**
- `brand`: Brand identifier (e.g., "UEG", "IBAT")
- `migrationS3Bucket`: S3 bucket containing legacy applications
- `migrationS3Path`: Folder path within the bucket

## Step 3: Create Brand Mapping File

Create a new mapping file: `src/oap/mappings/{brand}-mappings.ts`

```typescript
import { ApplicationMappingConfig } from './ueg-mappings';

export const YOUR_BRAND_APPLICATION_MAPPINGS: ApplicationMappingConfig = {
  directFields: {
    // Map legacy fields to new fields
    "data.firstName": "firstName",
    "data.lastName": "lastName",
    "data.email": "email",
    // Add all your field mappings here
  },

  documentFields: {
    // Map document fields
    "data.cvFile": "CV",
    "data.photoFile": "photograph",
    // Add document mappings
  },

  arrayFields: {
    // Map array fields that need subsection processing
    "data.educationHistory": "educationInfo",
    "data.workExperience": "workExperienceInfo",
  },

  subsectionMappings: {
    // Define how to transform objects within arrays
    educationInfo: {
      "schoolName": "universityName",
      "degree": "degree",
      "graduationYear": "examDate",
    },
  },

  picklistMappings: {
    // Map legacy picklist values to new values
    level: {
      "Bachelor": "Undergraduate",
      "Master": "Postgraduate",
    },
  },

  transformationRules: {
    // Define data transformation rules
    birthDate: {
      type: "date_format",
      from_format: "ISO",
      to_format: "YYYY-MM-DD"
    },
    isActive: {
      type: "boolean_conversion",
      true_values: ["1", "true", true],
      false_values: ["0", "false", false]
    }
  }
};
```

## Step 4: Register Brand Mapping

Update `src/oap/mappings/index.ts` to include your brand:

```typescript
import { YOUR_BRAND_APPLICATION_MAPPINGS } from './your-brand-mappings';

export const BRAND_MAPPINGS: Record<string, ApplicationMappingConfig> = {
  'UEG': UEG_APPLICATION_MAPPINGS,
  'IBAT': IBAT_APPLICATION_MAPPINGS,
  'YOUR_BRAND': YOUR_BRAND_APPLICATION_MAPPINGS,
};
```

## Step 5: Test Migration

1. **Dry Run**: Test with a small dataset first:
   ```bash
   curl -X POST "https://api.example.com/oap/migrateapplications" \
     -H "x-api-key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "oapName": "YOUR_OAP_NAME",
       "mode": "STUDENT",
       "dryRun": true
     }'
   ```

2. **Review Results**: Check the response for any mapping issues or errors.

3. **Production Run**: Once satisfied, run without `dryRun`:
   ```bash
   curl -X POST "https://api.example.com/oap/migrateapplications" \
     -H "x-api-key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "oapName": "YOUR_OAP_NAME",
       "mode": "STUDENT",
       "batchSize": 10
     }'
   ```

## Field Mapping Types

### Direct Fields
Simple one-to-one field mappings:
```typescript
"data.firstName": "firstName"
```

### List Mappings
Map single source field to multiple target fields:
```typescript
"data.correspondenceLanguage": ["correspondenceLanguage", "correspondenceLanguageDisplayName"]
```

### Document Fields
Map file arrays to document types:
```typescript
"data.cvFile": "CV"
```

### Array Fields
Map arrays that need subsection processing:
```typescript
"data.educationHistory": "educationInfo"
```

### Subsection Mappings
Transform objects within arrays:
```typescript
educationInfo: {
  "schoolName": "universityName",
  "degree": "degree"
}
```

## Transformation Rules

### Date Format
```typescript
birthDate: {
  type: "date_format",
  from_format: "ISO",
  to_format: "YYYY-MM-DD"
}
```

### Boolean Conversion
```typescript
isActive: {
  type: "boolean_conversion",
  true_values: ["1", "true", true],
  false_values: ["0", "false", false]
}
```

### Concatenation
```typescript
fullName: {
  type: "concatenate",
  fields: ["firstName", "lastName"],
  separator: " "
}
```

### Default Values
```typescript
source: {
  type: "default_value",
  value: "legacy_migration"
}
```

### Type Conversion
```typescript
duration: {
  type: "type_conversion",
  target_type: "number"
},
userId: {
  type: "type_conversion",
  target_type: "string"
}
```

**Supported target types:**
- `number`: Convert to floating point number
- `integer`: Convert to integer
- `string`: Convert to string
- `boolean`: Convert to boolean
- `array`: Convert to array (with optional delimiter)
- `object`: Convert to object

## Troubleshooting

### Common Issues

1. **Brand Not Found**: Ensure your brand is registered in `BRAND_MAPPINGS`
2. **S3 Path Not Found**: Verify the S3 bucket and path in OAP configuration
3. **Mapping Errors**: Check field paths match your legacy data structure
4. **Document Processing Fails**: Ensure document arrays follow expected format

### Debugging

Enable detailed logging by checking the API response for:
- `configUsed`: Shows the actual configuration being used
- `results`: Individual application processing results
- Error messages with specific field mapping issues
