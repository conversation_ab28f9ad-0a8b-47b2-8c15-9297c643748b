{"PK": "APPLICATION_MIGRATION", "SK": "EXAMPLE_OAP_STUDENT_APPLICATION_MIGRATION", "s3_config": {"bucket": "your-migration-bucket", "file_name": "legacy-applications/example-oap-applications.json", "file_type": "json"}, "field_mappings": {"firstName": "personalInfo.firstName", "lastName": "personalInfo.lastName", "email": "contactInfo.email", "phone": "contactInfo.phone", "dateOfBirth": "personalInfo.dateOfBirth", "address": "contactInfo.address", "city": "contactInfo.city", "state": "contactInfo.state", "zipCode": "contactInfo.zipCode", "country": "contactInfo.country", "programOfInterest": "academicInfo.programOfInterest", "levelOfStudy": "academicInfo.levelOfStudy", "intakeDate": "academicInfo.intakeDate", "previousEducation": "academicInfo.previousEducation", "workExperience": "professionalInfo.workExperience", "englishProficiency": "academicInfo.englishProficiency", "applicationStatus": "status", "submissionDate": "submittedAt", "lastUpdated": "updatedAt"}, "picklist_mappings": {"academicInfo.levelOfStudy": {"Bachelor": "Undergraduate", "Master": "Postgraduate", "PhD": "Doctorate", "Certificate": "ShortCourses", "Diploma": "Diploma"}, "contactInfo.country": {"US": "United States", "UK": "United Kingdom", "CA": "Canada", "AU": "Australia", "IN": "India", "DE": "Germany"}, "academicInfo.englishProficiency": {"IELTS": "IELTS", "TOEFL": "TOEFL", "PTE": "PTE Academic", "Native": "Native Speaker", "Other": "Other"}, "status": {"submitted": "Submitted", "under_review": "Under Review", "accepted": "Accepted", "rejected": "Rejected", "waitlisted": "Waitlisted"}}, "transformation_rules": {"personalInfo.fullName": {"type": "concatenate", "fields": ["firstName", "lastName"], "separator": " "}, "personalInfo.dateOfBirth": {"type": "date_format", "from_format": "MM/DD/YYYY", "to_format": "YYYY-MM-DD"}, "academicInfo.intakeDate": {"type": "date_format", "from_format": "MM/DD/YYYY", "to_format": "ISO"}, "applicationId": {"type": "default_value", "value": "MIGRATED_APP"}, "source": {"type": "default_value", "value": "legacy_migration"}}, "validation_rules": {"required_fields": ["personalInfo.firstName", "personalInfo.lastName", "contactInfo.email", "academicInfo.programOfInterest"], "email_validation": true, "phone_validation": false}, "migration_settings": {"skip_duplicates": true, "update_existing": false, "backup_original": true, "max_retries": 3, "retry_delay_ms": 1000}}