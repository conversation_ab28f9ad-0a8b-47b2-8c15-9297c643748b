# Data Transformation Examples

This document shows examples of how the migration service transforms legacy application data.

## 1. Country Code Mapping

### Input (Legacy Data)
```json
{
  "citizenship": "United States",
  "countryOfBirth": "United Kingdom",
  "correspondenceCountry": "Germany",
  "educationInfo": [
    {
      "awardingCountry": "India"
    }
  ]
}
```

### Output (Transformed Data)
```json
{
  "citizenshipDisplayName": "United States",
  "citizenship": "US",
  "countryDisplayName": "United Kingdom",
  "country": "GB",
  "correspondenceCountryDisplayName": "Germany",
  "correspondenceCountry": "DE",
  "educationInfo": [
    {
      "awardingCountryDisplayName": "India",
      "awardingCountry": "IN"
    }
  ]
}
```

## 2. Phone Number Formatting

### Input (Legacy Data)
```json
{
  "phoneNumber": "+************",
  "otherNumber": "12324354646"
}
```

### Output (Transformed Data)
```json
{
  "phoneNumber": {
    "number": "291111111",
    "numberWithCode": "+************",
    "dialCode": "375",
    "countryCode": "by"
  },
  "otherNumber": {
    "number": "2324354646", 
    "numberWithCode": "12324354646",
    "dialCode": "1",
    "countryCode": "us"
  }
}
```

## 3. Document ID Structure

### Input (Legacy Data)
```json
{
  "data": {
    "fileCV": [
      {
        "originalFileName": "cv.pdf",
        "filePath": "uuid/CV/hash.pdf"
      }
    ],
    "photo": [
      {
        "originalFileName": "photo.jpg", 
        "filePath": "uuid/ID/Passport/hash.jpg"
      }
    ],
    "educationSchoolsCertificateFile": [
      {
        "originalFileName": "diploma.pdf",
        "filePath": "uuid/Degree certificate/hash.pdf"
      }
    ]
  }
}
```

### Output (Transformed Data)
```json
{
  "CV": [
    {
      "documentId": "48051a1a-b031-4e45-ae46-728add8b7177"
    }
  ],
  "photograph": [
    {
      "documentId": "deef8db5-cd88-40ce-a466-10cdf28c3700"
    }
  ],
  "HEEQ": [
    {
      "documentId": "f6cebe15-a74f-4b90-b009-fedf1e49a95a"
    }
  ]
}
```

### Document Storage (Separate Database)
```json
{
  "PK": "<EMAIL>",
  "SK": "UEG_6d2eeb7c-452e-11f0-889a-fa8c60dfa742_CV_48051a1a-b031-4e45-ae46-728add8b7177",
  "applicationId": "6d2eeb7c-452e-11f0-889a-fa8c60dfa742",
  "documentId": "48051a1a-b031-4e45-ae46-728add8b7177",
  "documentType": "CV",
  "documentName": "cv.pdf",
  "filePath": "uuid/CV/hash.pdf",
  "s3BucketName": "campusnet-sf-sync-stage",
  "migrationMetadata": {
    "migratedAt": "2025-01-07T10:00:00.000Z",
    "sourceSystem": "legacy_s3",
    "originalFieldName": "fileCV"
  }
}
```

## 4. Complete Transformation Example

### Input (Legacy Application)
```json
{
  "_id": "6846d56747ab090740059b32",
  "uuid": "6d2eeb7c-452e-11f0-889a-fa8c60dfa742",
  "userId": 761,
  "data": {
    "firstName": "John",
    "legalFamilyName": "Doe",
    "personalEmail": "<EMAIL>",
    "dateOfBirth": "1990-05-15T00:00:00+00:00",
    "citizenship": "United States",
    "countryOfBirth": "Canada",
    "phoneHome": "+1234567890",
    "phone": "+************",
    "correspondenceCountry": "United Kingdom",
    "isReferFriend": "1",
    "fileCV": [
      {
        "originalFileName": "john_cv.pdf",
        "filePath": "6d2eeb7c-452e-11f0-889a-fa8c60dfa742/CV/cv_hash.pdf"
      }
    ],
    "educationSchools": [
      {
        "educationSchoolsType": "graduation",
        "educationSchoolsDegree": "Bachelor of Science",
        "educationSchoolsCountry": "India",
        "educationSchoolsCity": "Mumbai"
      }
    ]
  }
}
```

### Output (Transformed Application)
```json
{
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "birthDate": "1990-05-15",
  "citizenshipDisplayName": "United States",
  "citizenship": "US",
  "countryDisplayName": "Canada",
  "country": "CA",
  "phoneNumber": {
    "number": "234567890",
    "numberWithCode": "+1234567890", 
    "dialCode": "1",
    "countryCode": "us"
  },
  "otherNumber": {
    "number": "9876543210",
    "numberWithCode": "+************",
    "dialCode": "91", 
    "countryCode": "in"
  },
  "correspondenceCountryDisplayName": "United Kingdom",
  "correspondenceCountry": "GB",
  "isReferFriend": true,
  "CV": [
    {
      "documentId": "48051a1a-b031-4e45-ae46-728add8b7177"
    }
  ],
  "educationInfo": [
    {
      "educationDetailsEntryType": "graduation",
      "degree": "Bachelor of Science",
      "awardingCountryDisplayName": "India",
      "awardingCountry": "IN",
      "educationCity": "Mumbai"
    }
  ],
  "migrationMetadata": {
    "migratedAt": "2025-01-07T10:00:00.000Z",
    "sourceSystem": "legacy_s3",
    "migrationVersion": "1.0",
    "originalId": "6846d56747ab090740059b32",
    "originalUuid": "6d2eeb7c-452e-11f0-889a-fa8c60dfa742"
  }
}
```

## 5. List Mapping (Multiple Fields from Single Source)

### Input (Legacy Data)
```json
{
  "data": {
    "correspondenceLanguage": "german",
    "title": "dr",
    "duration": "12"
  }
}
```

### Output (Transformed Data)
```json
{
  "correspondenceLanguage": "german",
  "correspondenceLanguageDisplayName": "german",
  "title": "dr",
  "titleDisplayName": "dr",
  "duration": 12
}
```

## 6. Type Conversion

### Input (Legacy Data)
```json
{
  "duration": "12",
  "userId": 761,
  "isActive": "1"
}
```

### Output (Transformed Data)
```json
{
  "duration": 12,
  "userId": "761",
  "isActive": true
}
```

## 7. Document Field Mapping (Salesforce Compatible)

### Input (Legacy Data)
```json
{
  "data": {
    "educationSchoolsCertificateFile": [
      {
        "name": "certificate.pdf",
        "url": "https://example.com/file.pdf"
      }
    ]
  }
}
```

### Output (Transformed Data)
```json
{
  "HEEQ": [
    {
      "documentId": "6331749a-bff6-41d4-9a7c-f6782849481a"
    }
  ]
}
```

## 8. Array Field Date Transformation

### Input (Legacy Data)
```json
{
  "educationSchools": [
    {
      "startDate": "2020-09-01T00:00:00.000Z",
      "endDate": "2024-06-30T23:59:59.999Z"
    }
  ]
}
```

### Output (Transformed Data)
```json
{
  "educationSchools": [
    {
      "startDate": "2020-09-01",
      "endDate": "2024-06-30"
    }
  ]
}
```

## Transformation Rules Applied

1. **Field Mapping**: `data.firstName` → `firstName`
2. **List Mapping**: Single source field mapped to multiple target fields
3. **Date Formatting**: All date fields converted to YYYY-MM-DD format (including array fields)
4. **Country Field Convention**: Creates both DisplayName (full name) and code (ISO) fields
5. **Phone Number Formatting**: Phone strings converted to structured objects
6. **Boolean Conversion**: `"1"` converted to `true`
7. **Type Conversion**: String numbers converted to actual numbers
8. **Document Processing**: File arrays converted to document ID references with Salesforce-compatible types
9. **Subsection Mapping**: Education array objects transformed with new field names and date formatting
10. **UUID Generation**: Document IDs generated as proper UUIDs

## Benefits

- **Standardized Data**: All country references use ISO codes
- **Structured Phone Numbers**: Phone numbers include dial codes and country codes
- **Unique Document IDs**: Proper UUID format for document identification
- **Consistent Format**: All applications follow the same transformed structure
- **Traceability**: Migration metadata tracks transformation details
